package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/version"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/middleware"
	"github.com/temizlik-delisi/pkg/state"
)

func VersionRoutes(r *gin.RouterGroup, s version.Service) {
	g := r.Group("/version")
	g.GET("", middleware.FromClient(), middleware.Authorized(), getVersion(s))
}

// @Summary Get Version
// @Description Get Version
// @Tags Version Endpoints
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /version [GET]
func getVersion(s version.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetVersion(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get Version Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_get", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get Version",
			Message:     "Version Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
