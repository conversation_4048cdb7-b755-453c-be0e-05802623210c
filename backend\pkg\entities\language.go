package entities

import (
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type Language struct {
	Base

	Name      string    `json:"name" example:"Turkish"`
	Code      string    `json:"code" example:"TR"`
	Level     string    `json:"level" example:"basic"` // basic, conversational, fluent, native
	CleanerID uuid.UUID `json:"cleaner_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

func (l *Language) Response() dtos.ResponseForLanguage {
	var resp dtos.ResponseForLanguage

	resp.ID = l.ID.String()
	resp.CreatedAt = l.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Name = l.Name
	resp.Code = l.Code
	resp.Level = l.Level
	resp.CleanerID = l.CleanerID.String()

	return resp
}
