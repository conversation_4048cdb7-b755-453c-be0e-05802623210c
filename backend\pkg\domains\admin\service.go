package admin

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	ServiceCategoryGetAll(ctx context.Context, page, per_page int, approved int, is_main bool) (*dtos.PaginatedData, error)
	ServiceCategoryCreate(ctx context.Context, req dtos.RequestForCreateServiceCategory) error
	ServiceCategoryDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error
	ServiceCategoryUpdate(ctx context.Context, req dtos.RequestForUpdateServiceCategory) error

	CommentGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error)
	CommentDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error

	BlogCreate(ctx context.Context, req dtos.RequestForCreateBlog) error
	BlogDelete(ctx context.Context, req dtos.RequestForDeleteBlog) error
	BlogUpdate(ctx context.Context, req dtos.RequestForUpdateBlog) error
	BlogGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error)
	BlogGetByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error)

	OrderGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error)
	OrderGetByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error)
	OrderDelete(ctx context.Context, id uuid.UUID) error

	PreferenceCreate(ctx context.Context, req dtos.RequestForCreatePreference) error
	PreferenceUpdate(ctx context.Context, req dtos.RequestForUpdatePreference) error
	PreferenceDelete(ctx context.Context, req dtos.RequestForDeletePreference) error
	PreferenceGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error)
	PreferenceGetByID(ctx context.Context, id uuid.UUID) (*entities.PreferenceResponse, error)

	NewVersion(ctx context.Context, req dtos.RequestForNewVersion) error

	VerificationApprovedTC(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) ServiceCategoryGetAll(ctx context.Context, page, per_page int, approved int, is_main bool) (*dtos.PaginatedData, error) {
	return s.repository.serviceCategoryGetAll(ctx, page, per_page, approved, is_main)
}

func (s *service) ServiceCategoryCreate(ctx context.Context, req dtos.RequestForCreateServiceCategory) error {
	return s.repository.serviceCategoryCreate(ctx, req)
}

func (s *service) ServiceCategoryDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error {
	return s.repository.serviceCategoryDelete(ctx, req)
}

func (s *service) ServiceCategoryUpdate(ctx context.Context, req dtos.RequestForUpdateServiceCategory) error {
	return s.repository.serviceCategoryUpdate(ctx, req)
}

func (s *service) CommentGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error) {
	return s.repository.commentGetAll(ctx, page, per_page, approved)
}

func (s *service) CommentDelete(ctx context.Context, req dtos.RequestForDeleteServiceCategory) error {
	return s.repository.commentDelete(ctx, req)
}

func (s *service) BlogCreate(ctx context.Context, req dtos.RequestForCreateBlog) error {
	return s.repository.blogCreate(ctx, req)
}

func (s *service) BlogDelete(ctx context.Context, req dtos.RequestForDeleteBlog) error {
	return s.repository.blogDelete(ctx, req)
}

func (s *service) BlogUpdate(ctx context.Context, req dtos.RequestForUpdateBlog) error {
	return s.repository.blogUpdate(ctx, req)
}

func (s *service) BlogGetAll(ctx context.Context, page, per_page int, approved int) (*dtos.PaginatedData, error) {
	return s.repository.blogGetAll(ctx, page, per_page, approved)
}

func (s *service) BlogGetByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error) {
	return s.repository.blogGetByID(ctx, id)
}

func (s *service) OrderGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error) {
	return s.repository.orderGetAll(ctx, req)
}

func (s *service) OrderGetByID(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error) {
	return s.repository.orderGetByID(ctx, id)
}

func (s *service) OrderDelete(ctx context.Context, id uuid.UUID) error {
	return s.repository.orderDelete(ctx, id)
}

func (s *service) PreferenceCreate(ctx context.Context, req dtos.RequestForCreatePreference) error {
	return s.repository.preferenceCreate(ctx, req)
}

func (s *service) PreferenceUpdate(ctx context.Context, req dtos.RequestForUpdatePreference) error {
	return s.repository.preferenceUpdate(ctx, req)
}

func (s *service) PreferenceDelete(ctx context.Context, req dtos.RequestForDeletePreference) error {
	return s.repository.preferenceDelete(ctx, req)
}

func (s *service) PreferenceGetAll(ctx context.Context, req dtos.RequestForPaginate) (*dtos.PaginatedData, error) {
	return s.repository.preferenceGetAll(ctx, req)
}

func (s *service) PreferenceGetByID(ctx context.Context, id uuid.UUID) (*entities.PreferenceResponse, error) {
	return s.repository.preferenceGetByID(ctx, id)
}

func (s *service) NewVersion(ctx context.Context, req dtos.RequestForNewVersion) error {
	return s.repository.newVersion(ctx, req)
}

func (s *service) VerificationApprovedTC(ctx context.Context, id uuid.UUID) error {
	return s.repository.verificationApprovedTC(ctx, id)
}
