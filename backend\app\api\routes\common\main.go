package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/address"
	"github.com/temizlik-delisi/pkg/domains/blog"
	"github.com/temizlik-delisi/pkg/domains/comment"
	"github.com/temizlik-delisi/pkg/domains/license"
	"github.com/temizlik-delisi/pkg/domains/order"
	"github.com/temizlik-delisi/pkg/domains/profil"
	"github.com/temizlik-delisi/pkg/domains/serviceCategory"
	"github.com/temizlik-delisi/pkg/middleware"
)

func CommonRoutes(r *gin.RouterGroup, bs blog.Service, os order.Service, as address.Service, ls license.Service, ps profil.Service, sc serviceCategory.Service, cs comment.Service) {
	c_group := r.Group("/common")

	c_group.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	{
		c_group.GET("/blog", GetAllBlog(bs))
		c_group.GET("/blog/:id", GetBlogByID(bs))

		c_group.POST("/order", middleware.OrderCountControl(), CreateOrder(os))
		c_group.GET("/order", GetOrders(os))
		c_group.GET("/order/:id", GetOrder(os))
		c_group.GET("/my-order", GetMyOrders(os))
		c_group.DELETE("/order/:id", DeleteOrder(os))
		c_group.PATCH("/order/:id", UpdateOrder(os))

		c_group.GET("/license", GetLicense(ls))

		c_group.GET("/service-category", GetAllServiceCategory(sc))

		c_group.POST("/comment", CreateComment(cs))
		c_group.GET("/comment", GetComments(cs))
		c_group.DELETE("/comment/:id", DeleteComment(cs))

		c_group.POST("/profile", UpdateProfile(ps))
		c_group.GET("/profile/:id", GetProfileByID(ps))
		c_group.GET("/my-profile", GetMyProfile(ps))

		c_group.POST("/address", CreateAddress(as))
		c_group.PATCH("/address/:id", UpdateAddress(as))
		c_group.DELETE("/address/:id", DeleteAddress(as))
		c_group.GET("/address", GetAddresses(as))
		c_group.GET("/address/:id", GetAddressByID(as))
	}

}
