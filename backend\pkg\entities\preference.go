package entities

type Preference struct {
	Base

	Name            string `json:"name" example:"ironing"`
	Description     string `json:"description" example:"true"`
	ApprovedByAdmin bool   `json:"approved_by_admin" gorm:"default:false" example:"false"`
}

type PreferenceResponse struct {
	ID              string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt       string `json:"created_at" example:"2021-01-01 00:00:00"`
	Name            string `json:"name" example:"ironing"`
	Description     string `json:"description" example:"true"`
	ApprovedByAdmin bool   `json:"approved_by_admin" gorm:"default:false" example:"false"`
}

// PreferenceWithUsers - Preference with users who have this preference
type PreferenceWithUsers struct {
	ID              string     `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt       string     `json:"created_at" example:"2021-01-01 00:00:00"`
	Name            string     `json:"name" example:"ironing"`
	Description     string     `json:"description" example:"Ironing service"`
	ApprovedByAdmin bool       `json:"approved_by_admin" example:"true"`
	UserCount       int        `json:"user_count" example:"5"`
	Users           []UserInfo `json:"users"`
}

// UserInfo - Simplified user info for joins
type UserInfo struct {
	ID       string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Username string `json:"username" example:"john_doe"`
	Name     string `json:"name" example:"John"`
	Surname  string `json:"surname" example:"Doe"`
	Email    string `json:"email" example:"<EMAIL>"`
	AddedAt  string `json:"added_at" example:"2021-01-01 00:00:00"` // UserPreference created_at
}

func (p *Preference) Response() PreferenceResponse {
	var resp PreferenceResponse

	resp.ID = p.ID.String()
	resp.CreatedAt = p.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Name = p.Name
	resp.Description = p.Description
	resp.ApprovedByAdmin = p.ApprovedByAdmin

	return resp
}
