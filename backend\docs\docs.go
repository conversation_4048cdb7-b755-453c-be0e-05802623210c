// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/blog/create": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Create Blog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Create Blog",
                "parameters": [
                    {
                        "description": "request payload for create blog",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateBlog"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/blog/delete": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Delete Blog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Delete Blog",
                "parameters": [
                    {
                        "description": "request payload for delete blog",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeleteBlog"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/blog/get-all": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Blog",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get All Blog",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "approved number 1: approved, 2: not approved, if you send any other number, it will return all blogs",
                        "name": "approved",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/blog/get-by-id": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Blog By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get Blog By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "blog id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/blog/update": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Blog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Update Blog",
                "parameters": [
                    {
                        "description": "request payload for update blog",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateBlog"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/comment/delete": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Delete Comment",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Delete Comment",
                "parameters": [
                    {
                        "description": "request payload for delete comment",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeleteServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/comment/get-all": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Comment",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get All Comment",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "approved number 1: approved, 2: not approved, if you send any other number, it will return all comments",
                        "name": "approved",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/order/delete": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Delete Order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Delete Order",
                "parameters": [
                    {
                        "description": "request payload for delete order",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeleteServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/order/get-all": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Order",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get All Order",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/order/get-by-id": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Order By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get Order By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "order id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/preference/create": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Create Preference",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Create Preference",
                "parameters": [
                    {
                        "description": "request payload for create preference",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreatePreference"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/preference/delete": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Delete Preference",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Delete Preference",
                "parameters": [
                    {
                        "description": "request payload for delete preference",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeletePreference"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/preference/get-all": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Preference",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get All Preference",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "approved number 1: approved, 2: not approved, if you send any other number, it will return all preferences",
                        "name": "approved",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/preference/get-by-id": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Preference By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get Preference By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "preference id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/preference/update": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Preference",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Update Preference",
                "parameters": [
                    {
                        "description": "request payload for update preference",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdatePreference"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/service-category/create": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Create Service Category",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Create Service Category",
                "parameters": [
                    {
                        "description": "request payload for create service category",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/service-category/delete": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Delete Service Category",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Delete Service Category",
                "parameters": [
                    {
                        "description": "request payload for delete service category",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeleteServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/service-category/get-all": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Service Category",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Get All Service Category",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "approved number 1: approved, 2: not approved, if you send any other number, it will return all service types",
                        "name": "approved",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/service-category/update": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Update Service Category",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Update Service Category",
                "parameters": [
                    {
                        "description": "request payload for update service category",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/verification/tc/approved": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Approved TC",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "Approved TC",
                "parameters": [
                    {
                        "description": "request payload for approved tc",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDeleteServiceCategory"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/version/new": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "New Version",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Admin Endpoints"
                ],
                "summary": "New Version",
                "parameters": [
                    {
                        "description": "request payload for new version",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForNewVersion"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/apple-login": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Apple Sign-In Login",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Apple Login",
                "parameters": [
                    {
                        "description": "apple login request payload",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForAppleLogin"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/cleaner/criminal-record": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Upload Criminal Record Document",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-CriminalRecordDocument"
                ],
                "summary": "Upload Criminal Record Document",
                "parameters": [
                    {
                        "type": "file",
                        "description": "Criminal record document file",
                        "name": "criminal_record_file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/cleaner/language": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Languages",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-Language"
                ],
                "summary": "Get Languages",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetLanguagesResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Language",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-Language"
                ],
                "summary": "Create Language",
                "parameters": [
                    {
                        "description": "request payload for create language",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateLanguage"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/cleaner/language/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Language",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-Language"
                ],
                "summary": "Delete Language",
                "parameters": [
                    {
                        "type": "string",
                        "description": "language id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/cleaner/work-experience": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Work Experiences",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-WorkExperience"
                ],
                "summary": "Get Work Experiences",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetWorkExperiencesResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Work Experience",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-WorkExperience"
                ],
                "summary": "Create Work Experience",
                "parameters": [
                    {
                        "description": "Work Experience data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateWorkExperience"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/cleaner/work-experience/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Work Experience by ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-WorkExperience"
                ],
                "summary": "Get Work Experience",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Work Experience ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetWorkExperienceResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update Work Experience",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-WorkExperience"
                ],
                "summary": "Update Work Experience",
                "parameters": [
                    {
                        "description": "Work Experience data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateWorkExperience"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Work Experience",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-WorkExperience"
                ],
                "summary": "Delete Work Experience",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Work Experience ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/address": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Addresses",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Address"
                ],
                "summary": "Get Addresses",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginateResponseForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Address",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Address"
                ],
                "summary": "Create Address",
                "parameters": [
                    {
                        "description": "request payload for create address",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateAddress"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/address/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Address By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Address"
                ],
                "summary": "Get Address By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "address id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/entities.AddressResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Address",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Address"
                ],
                "summary": "Delete Address",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update Address",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Address"
                ],
                "summary": "Update Address",
                "parameters": [
                    {
                        "description": "request payload for update address",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateAddress"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/blog": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Blog",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Blog"
                ],
                "summary": "Get All Blog",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/common/blog/{id}": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get Blog By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Blog"
                ],
                "summary": "Get Blog By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "blog id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/common/comment": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Comments",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Comment"
                ],
                "summary": "Get Comments",
                "parameters": [
                    {
                        "type": "string",
                        "description": "if you send an account_id, it will return comments for that account, if you don't send an account_id, it will return comments for the current user",
                        "name": "account_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginatedData"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Comment",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Comment"
                ],
                "summary": "Create Comment",
                "parameters": [
                    {
                        "description": "create comment request payload",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateComment"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/comment/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Comment",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Comment"
                ],
                "summary": "Delete Comment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "comment id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/license": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get License",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get License",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/common/my-order": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get My Orders",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Get My Orders",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginateResponseForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/my-profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Customer Profile",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Profile"
                ],
                "summary": "Get Customer Profile",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/entities.ResponseForDetail"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/order": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Orders",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Get Orders",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "radius meters number",
                        "name": "radius_meters",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginateResponseForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Create Order",
                "parameters": [
                    {
                        "description": "request payload for create order",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateOrder"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/order/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Order By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Get Order By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "order id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/entities.OrderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Delete Order",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update Order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Order"
                ],
                "summary": "Update Order",
                "parameters": [
                    {
                        "description": "request payload for update order",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateOrder"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/profile": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update Customer Profile",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Profile"
                ],
                "summary": "Update Customer Profile",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"1990-01-01\"",
                        "description": "Date of birth",
                        "name": "date_of_birth",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"+905321234567\"",
                        "description": "Phone number",
                        "name": "phone",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"Turkey\"",
                        "description": "Country",
                        "name": "country",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"John\"",
                        "description": "Name",
                        "name": "name",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"Doe\"",
                        "description": "Surname",
                        "name": "surname",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Profile photo",
                        "name": "profile_photo",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/profile/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Profile By ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-Profile"
                ],
                "summary": "Get Profile By ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "profile id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/entities.ResponseForDetail"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/common/service-category": {
            "get": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Get All Service Category",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Common-ServiceCategory"
                ],
                "summary": "Get All Service Category",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/entities.ServiceCategoryResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/customer-preference": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get All Customer Preference",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Customer"
                ],
                "summary": "Get All Customer Preference",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create Customer Preference",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Customer"
                ],
                "summary": "Create Customer Preference",
                "parameters": [
                    {
                        "description": "request payload for create customer preference",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateCustomerPreference"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/customer-preference/user": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Customer With Preferences",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Customer"
                ],
                "summary": "Get Customer With Preferences",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "page number",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "per_page number",
                        "name": "per_page",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/customer-preference/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Customer Preference",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Customer"
                ],
                "summary": "Delete Customer Preference",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/delete-cleaner": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Cleaner",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Delete Cleaner",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/delete-customer": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Customer",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Delete Customer",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/google-login": {
            "post": {
                "security": [
                    {
                        "none": []
                    }
                ],
                "description": "Google Login",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Auth"
                ],
                "summary": "Google Login",
                "parameters": [
                    {
                        "description": "google login request payload",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForGoogleLogin"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/verification/criminal-record": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Criminal Record Document",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Cleaner-CriminalRecordDocument"
                ],
                "summary": "Delete Criminal Record Document",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusCreated"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusBadRequest"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/dtos.GetResponseStatusInternalServerError"
                        }
                    }
                }
            }
        },
        "/verification/tc-no": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Verify TC",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Verification Endpoints"
                ],
                "summary": "Verify TC",
                "parameters": [
                    {
                        "description": "request payload for verify tc",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForVerifyTC"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/version": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Version",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Version Endpoints"
                ],
                "summary": "Get Version",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dtos.GetLanguagesResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dtos.ResponseForLanguage"
                    }
                },
                "status": {
                    "type": "integer",
                    "example": 200
                }
            }
        },
        "dtos.GetResponseStatusBadRequest": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "example": "Bad Request"
                },
                "status": {
                    "type": "integer",
                    "example": 400
                }
            }
        },
        "dtos.GetResponseStatusCreated": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "string",
                    "example": ".. created or deleted successfully"
                },
                "status": {
                    "type": "integer",
                    "example": 201
                }
            }
        },
        "dtos.GetResponseStatusInternalServerError": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "example": "Internal Server Error"
                },
                "status": {
                    "type": "integer",
                    "example": 500
                }
            }
        },
        "dtos.GetWorkExperienceResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/dtos.ResponseForWorkExperience"
                },
                "status": {
                    "type": "integer",
                    "example": 200
                }
            }
        },
        "dtos.GetWorkExperiencesResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dtos.ResponseForWorkExperience"
                    }
                },
                "status": {
                    "type": "integer",
                    "example": 200
                }
            }
        },
        "dtos.PaginateResponseForSwagger": {
            "type": "object",
            "properties": {
                "data": {
                    "$ref": "#/definitions/dtos.PaginatedData"
                },
                "status": {
                    "type": "integer",
                    "example": 200
                }
            }
        },
        "dtos.PaginatedData": {
            "type": "object",
            "properties": {
                "is_last_page": {
                    "type": "boolean",
                    "example": false
                },
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "per_page": {
                    "type": "integer",
                    "example": 10
                },
                "rows": {
                    "type": "array",
                    "items": {
                        "type": "object"
                    }
                },
                "total": {
                    "type": "integer",
                    "example": 100
                },
                "total_pages": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "dtos.RequestForAppleLogin": {
            "type": "object",
            "required": [
                "account_type",
                "apple_code",
                "os",
                "phone_language",
                "purchase_id",
                "push_notif_token",
                "time_zone"
            ],
            "properties": {
                "account_type": {
                    "description": "1. Normal User 2. Cleaner",
                    "type": "integer",
                    "example": 1
                },
                "apple_code": {
                    "type": "string",
                    "example": "c123456789.0.abcd.efgh"
                },
                "last_version_build_number": {
                    "type": "integer",
                    "example": 3
                },
                "last_version_name": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "os": {
                    "type": "string",
                    "example": "ios"
                },
                "phone_language": {
                    "type": "string",
                    "example": "tr"
                },
                "purchase_id": {
                    "type": "string",
                    "example": "**********"
                },
                "push_notif_token": {
                    "type": "string",
                    "example": "**********"
                },
                "time_zone": {
                    "type": "string",
                    "example": "Europe/Istanbul"
                }
            }
        },
        "dtos.RequestForCreateAddress": {
            "type": "object",
            "required": [
                "city",
                "country",
                "lat",
                "line1",
                "lng",
                "title",
                "zip"
            ],
            "properties": {
                "city": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "district": {
                    "type": "string",
                    "example": "Bakirkoy"
                },
                "is_main": {
                    "type": "boolean",
                    "example": false
                },
                "lat": {
                    "type": "number",
                    "example": 41.0082
                },
                "line1": {
                    "type": "string",
                    "example": "Cumhuriyet Mah."
                },
                "line2": {
                    "type": "string",
                    "example": "Cumhuriyet Cd."
                },
                "line3": {
                    "type": "string",
                    "example": "Cumhuriyet Sk."
                },
                "lng": {
                    "type": "number",
                    "example": 28.9784
                },
                "title": {
                    "type": "string",
                    "example": "home"
                },
                "zip": {
                    "type": "string",
                    "example": "34000"
                }
            }
        },
        "dtos.RequestForCreateBlog": {
            "type": "object",
            "required": [
                "content",
                "summary",
                "title"
            ],
            "properties": {
                "content": {
                    "type": "string",
                    "example": "Blog content here..."
                },
                "image_url": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "summary": {
                    "type": "string",
                    "example": "Short summary of the blog"
                },
                "tags": {
                    "type": "string",
                    "example": "cleaning,tips,home"
                },
                "title": {
                    "type": "string",
                    "example": "Blog Title"
                }
            }
        },
        "dtos.RequestForCreateComment": {
            "type": "object",
            "required": [
                "rating",
                "target_account_id",
                "target_account_type"
            ],
            "properties": {
                "comment": {
                    "type": "string",
                    "example": "very good"
                },
                "just_rate": {
                    "type": "boolean",
                    "example": false
                },
                "rating": {
                    "type": "integer",
                    "example": 5
                },
                "target_account_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "target_account_type": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dtos.RequestForCreateCustomerPreference": {
            "type": "object",
            "required": [
                "preference_id"
            ],
            "properties": {
                "preference_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForCreateLanguage": {
            "type": "object",
            "required": [
                "code",
                "level",
                "name"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "TR"
                },
                "level": {
                    "description": "basic, conversational, fluent, native",
                    "type": "string",
                    "example": "conversational"
                },
                "name": {
                    "type": "string",
                    "example": "Turkish"
                }
            }
        },
        "dtos.RequestForCreateOrder": {
            "type": "object",
            "required": [
                "address_id",
                "description",
                "end_date",
                "price_per_cleaner",
                "room_number",
                "service_category_id",
                "start_date",
                "title"
            ],
            "properties": {
                "address_id": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "end_date": {
                    "type": "string"
                },
                "house_size": {
                    "type": "integer"
                },
                "how_many_cleaners": {
                    "type": "integer",
                    "default": 1
                },
                "price_per_cleaner": {
                    "type": "number"
                },
                "room_number": {
                    "type": "string"
                },
                "service_category_id": {
                    "type": "string"
                },
                "start_date": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "total_price": {
                    "type": "number"
                }
            }
        },
        "dtos.RequestForCreatePreference": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "true"
                },
                "name": {
                    "type": "string",
                    "example": "ironing"
                }
            }
        },
        "dtos.RequestForCreateServiceCategory": {
            "type": "object",
            "required": [
                "description",
                "min_duration",
                "service_name_en",
                "service_name_tr"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "standart home cleaning description"
                },
                "is_main": {
                    "type": "boolean",
                    "example": false
                },
                "main_category_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "min_duration": {
                    "type": "integer",
                    "example": 60
                },
                "service_name_en": {
                    "type": "string",
                    "example": "standart home cleaning"
                },
                "service_name_tr": {
                    "type": "string",
                    "example": "standart home cleaning"
                }
            }
        },
        "dtos.RequestForCreateWorkExperience": {
            "type": "object",
            "required": [
                "company",
                "country",
                "location",
                "start_date",
                "title"
            ],
            "properties": {
                "company": {
                    "type": "string",
                    "example": "ABC Cleaning Services"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "current": {
                    "type": "boolean",
                    "example": false
                },
                "description": {
                    "type": "string",
                    "example": "Responsible for residential cleaning services"
                },
                "end_date": {
                    "type": "string",
                    "example": "2023-12-31"
                },
                "location": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "start_date": {
                    "type": "string",
                    "example": "2020-01-01"
                },
                "title": {
                    "type": "string",
                    "example": "Senior Cleaner"
                }
            }
        },
        "dtos.RequestForDeleteBlog": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForDeletePreference": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForDeleteServiceCategory": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForGoogleLogin": {
            "type": "object",
            "required": [
                "account_type",
                "google_id_token",
                "os",
                "phone_language",
                "purchase_id",
                "push_notif_token",
                "time_zone"
            ],
            "properties": {
                "account_type": {
                    "description": "1. Client 2. Cleaner",
                    "type": "integer",
                    "example": 1
                },
                "google_id_token": {
                    "type": "string",
                    "example": "ya29.a0AfH6SMC..."
                },
                "last_version_build_number": {
                    "type": "integer",
                    "example": 3
                },
                "last_version_name": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "os": {
                    "type": "string",
                    "example": "android"
                },
                "phone_language": {
                    "type": "string",
                    "example": "tr"
                },
                "purchase_id": {
                    "type": "string",
                    "example": "**********"
                },
                "push_notif_token": {
                    "type": "string",
                    "example": "**********"
                },
                "time_zone": {
                    "type": "string",
                    "example": "Europe/Istanbul"
                }
            }
        },
        "dtos.RequestForNewVersion": {
            "type": "object",
            "properties": {
                "android_build_number": {
                    "type": "integer"
                },
                "android_force_update_build_number": {
                    "type": "integer"
                },
                "android_version_name": {
                    "type": "string"
                },
                "ios_build_number": {
                    "type": "integer"
                },
                "ios_force_update_build_number": {
                    "type": "integer"
                },
                "ios_version_name": {
                    "type": "string"
                },
                "is_force": {
                    "type": "boolean"
                }
            }
        },
        "dtos.RequestForUpdateAddress": {
            "type": "object",
            "required": [
                "city",
                "country",
                "id",
                "lat",
                "line1",
                "lng",
                "title",
                "zip"
            ],
            "properties": {
                "city": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "district": {
                    "type": "string",
                    "example": "Bakirkoy"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_main": {
                    "type": "boolean",
                    "example": false
                },
                "lat": {
                    "type": "number",
                    "example": 41.0082
                },
                "line1": {
                    "type": "string",
                    "example": "Cumhuriyet Mah."
                },
                "line2": {
                    "type": "string",
                    "example": "Cumhuriyet Cd."
                },
                "line3": {
                    "type": "string",
                    "example": "Cumhuriyet Sk."
                },
                "lng": {
                    "type": "number",
                    "example": 28.9784
                },
                "title": {
                    "type": "string",
                    "example": "home"
                },
                "zip": {
                    "type": "string",
                    "example": "34000"
                }
            }
        },
        "dtos.RequestForUpdateBlog": {
            "type": "object",
            "required": [
                "content",
                "id",
                "is_status",
                "summary",
                "title"
            ],
            "properties": {
                "content": {
                    "type": "string",
                    "example": "Blog content here..."
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "image_url": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "is_status": {
                    "type": "boolean",
                    "example": true
                },
                "summary": {
                    "type": "string",
                    "example": "Short summary of the blog"
                },
                "tags": {
                    "type": "string",
                    "example": "cleaning,tips,home"
                },
                "title": {
                    "type": "string",
                    "example": "Blog Title"
                }
            }
        },
        "dtos.RequestForUpdateOrder": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "address_id": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "end_date": {
                    "type": "string"
                },
                "house_size": {
                    "type": "integer"
                },
                "how_many_cleaners": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "price_per_cleaner": {
                    "type": "number"
                },
                "room_number": {
                    "type": "string"
                },
                "service_category_id": {
                    "type": "string"
                },
                "start_date": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "total_price": {
                    "type": "number"
                }
            }
        },
        "dtos.RequestForUpdatePreference": {
            "type": "object",
            "required": [
                "id",
                "is_active",
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "true"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_active": {
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "type": "string",
                    "example": "ironing"
                }
            }
        },
        "dtos.RequestForUpdateServiceCategory": {
            "type": "object",
            "required": [
                "description",
                "id",
                "is_status",
                "min_duration",
                "service_name_en",
                "service_name_tr"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "standart home cleaning description"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_status": {
                    "type": "boolean",
                    "example": true
                },
                "min_duration": {
                    "type": "integer",
                    "example": 60
                },
                "service_name_en": {
                    "type": "string",
                    "example": "standart home cleaning"
                },
                "service_name_tr": {
                    "type": "string",
                    "example": "standart home cleaning"
                }
            }
        },
        "dtos.RequestForUpdateWorkExperience": {
            "type": "object",
            "required": [
                "company",
                "country",
                "id",
                "location",
                "start_date",
                "title"
            ],
            "properties": {
                "company": {
                    "type": "string",
                    "example": "ABC Cleaning Services"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "current": {
                    "type": "boolean",
                    "example": false
                },
                "description": {
                    "type": "string",
                    "example": "Responsible for residential cleaning services"
                },
                "end_date": {
                    "type": "string",
                    "example": "2023-12-31"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "location": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "start_date": {
                    "type": "string",
                    "example": "2020-01-01"
                },
                "title": {
                    "type": "string",
                    "example": "Senior Cleaner"
                }
            }
        },
        "dtos.RequestForVerifyTC": {
            "type": "object",
            "required": [
                "tc_no"
            ],
            "properties": {
                "tc_no": {
                    "type": "string",
                    "example": "**********1"
                }
            }
        },
        "dtos.ResponseForLanguage": {
            "type": "object",
            "properties": {
                "cleaner_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "code": {
                    "type": "string",
                    "example": "TR"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "level": {
                    "description": "basic, conversational, fluent, native",
                    "type": "string",
                    "example": "conversational"
                },
                "name": {
                    "type": "string",
                    "example": "Turkish"
                }
            }
        },
        "dtos.ResponseForWorkExperience": {
            "type": "object",
            "properties": {
                "cleaner_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "company": {
                    "type": "string",
                    "example": "ABC Cleaning Services"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "current": {
                    "type": "boolean",
                    "example": false
                },
                "description": {
                    "type": "string",
                    "example": "Responsible for residential cleaning services"
                },
                "end_date": {
                    "type": "string",
                    "example": "2023-12-31"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "location": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "start_date": {
                    "type": "string",
                    "example": "2020-01-01"
                },
                "title": {
                    "type": "string",
                    "example": "Senior Cleaner"
                }
            }
        },
        "entities.AddressResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "account_type": {
                    "type": "integer",
                    "example": 1
                },
                "city": {
                    "type": "string",
                    "example": "Istanbul"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "district": {
                    "type": "string",
                    "example": "Bakirkoy"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_main": {
                    "type": "boolean",
                    "example": false
                },
                "lat": {
                    "type": "number",
                    "example": 41.0082
                },
                "line1": {
                    "type": "string",
                    "example": "Cumhuriyet Mah."
                },
                "line2": {
                    "type": "string",
                    "example": "Cumhuriyet Cd."
                },
                "line3": {
                    "type": "string",
                    "example": "Cumhuriyet Sk."
                },
                "lng": {
                    "type": "number",
                    "example": 28.9784
                },
                "title": {
                    "type": "string",
                    "example": "home"
                },
                "zip": {
                    "type": "string",
                    "example": "34000"
                }
            }
        },
        "entities.OrderResponse": {
            "type": "object",
            "properties": {
                "account": {
                    "$ref": "#/definitions/entities.UserResponse"
                },
                "address": {
                    "$ref": "#/definitions/entities.AddressResponse"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "description": {
                    "type": "string"
                },
                "distance": {
                    "type": "number"
                },
                "end_date": {
                    "type": "string"
                },
                "house_size": {
                    "type": "integer"
                },
                "how_many_cleaners": {
                    "type": "integer"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "price_per_cleaner": {
                    "type": "number"
                },
                "service_category": {
                    "type": "string"
                },
                "start_date": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "total_price": {
                    "type": "number"
                }
            }
        },
        "entities.ResponseForDetail": {
            "type": "object",
            "properties": {
                "apple_id": {
                    "type": "string",
                    "example": "**********"
                },
                "country": {
                    "type": "string",
                    "example": "Turkey"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "date_of_birth": {
                    "type": "string",
                    "example": "1990-01-01"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "google_id": {
                    "type": "string",
                    "example": "**********"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_profile_updated": {
                    "type": "boolean",
                    "example": true
                },
                "last_login_date": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "last_version_build_number": {
                    "type": "integer",
                    "example": 3
                },
                "last_version_name": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "name": {
                    "type": "string",
                    "example": "John"
                },
                "os": {
                    "type": "string",
                    "example": "android"
                },
                "phone": {
                    "type": "string",
                    "example": "**********"
                },
                "phone_language": {
                    "type": "string",
                    "example": "tr"
                },
                "profile_photo_url": {
                    "type": "string",
                    "example": "https://minio.example.com/profile-photos/photo.jpg"
                },
                "provider": {
                    "type": "string",
                    "example": "google"
                },
                "purchase_id": {
                    "type": "string",
                    "example": "**********"
                },
                "push_notif_token": {
                    "type": "string",
                    "example": "**********"
                },
                "reference_id": {
                    "type": "string",
                    "example": "ABC45678"
                },
                "surname": {
                    "type": "string",
                    "example": "Doe"
                },
                "time_zone": {
                    "type": "string",
                    "example": "Europe/Istanbul"
                }
            }
        },
        "entities.ServiceCategoryResponse": {
            "type": "object",
            "properties": {
                "approved_by_admin": {
                    "type": "boolean",
                    "example": false
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01 00:00:00"
                },
                "description": {
                    "type": "string",
                    "example": "standart home cleaning description"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "is_main": {
                    "type": "boolean",
                    "example": false
                },
                "min_duration": {
                    "type": "integer",
                    "example": 60
                },
                "permitted_lisance_types": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    },
                    "example": [
                        1,
                        2,
                        3
                    ]
                },
                "service_name_en": {
                    "type": "string",
                    "example": "standart home cleaning"
                },
                "service_name_tr": {
                    "type": "string",
                    "example": "standart home cleaning"
                },
                "sub_categories": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/entities.ServiceCategoryResponse"
                    }
                }
            }
        },
        "entities.UserResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "account_type": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "type": "string",
                    "example": "John"
                },
                "surname": {
                    "type": "string",
                    "example": "Doe"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "**************:8000",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "Temizlik Delisi API",
	Description:      "Temizlik Delisi API Documentation",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
