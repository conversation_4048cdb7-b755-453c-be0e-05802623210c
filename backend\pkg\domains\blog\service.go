package blog

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	GetAllBlogs(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	GetBlogByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetAllBlogs(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.getAllBlogs(ctx, page, per_page)
}

func (s *service) GetBlogByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error) {
	return s.repository.getBlogByID(ctx, id)
}
