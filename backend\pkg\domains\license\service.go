package license

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	GetLicense(ctx context.Context) (entities.LicenseResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetLicense(ctx context.Context) (entities.LicenseResponse, error) {
	return s.repository.getLicense(ctx)
}
