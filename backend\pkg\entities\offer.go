package entities

import (
	"github.com/google/uuid"
)

type Offer struct {
	Base

	OrderID           uuid.UUID `json:"order_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderID          uuid.UUID `json:"bidder_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderAccountType int       `json:"bidder_account_type" example:"1"`
	Description       string    `json:"description" example:"I can clean your house"`
	NewStartDate      string    `json:"new_start_date" example:"2021-01-01 00:00:00"`
	NewEndDate        string    `json:"new_end_date" example:"2021-01-01 00:00:00"`
	NewPrice          float64   `json:"new_price" example:"100"`
	IsAccepted        bool      `json:"is_accepted" gorm:"default:false" example:"false"`
}

type OfferResponse struct {
	ID                string  `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt         string  `json:"created_at" example:"2021-01-01 00:00:00"`
	OrderID           string  `json:"order_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderID          string  `json:"bidder_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	BidderAccountType int     `json:"bidder_account_type" example:"1"`
	Description       string  `json:"description" example:"I can clean your house"`
	NewStartDate      string  `json:"new_start_date" example:"2021-01-01 00:00:00"`
	NewEndDate        string  `json:"new_end_date" example:"2021-01-01 00:00:00"`
	NewPrice          float64 `json:"new_price" example:"100"`
}

func (o *Offer) Response() OfferResponse {
	var resp OfferResponse

	resp.ID = o.ID.String()
	resp.CreatedAt = o.CreatedAt.Format("2006-01-02 15:04:05")
	resp.OrderID = o.OrderID.String()
	resp.BidderID = o.BidderID.String()
	resp.BidderAccountType = o.BidderAccountType
	resp.Description = o.Description
	resp.NewStartDate = o.NewStartDate
	resp.NewEndDate = o.NewEndDate
	resp.NewPrice = o.NewPrice

	return resp
}
