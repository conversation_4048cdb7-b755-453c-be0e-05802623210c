package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/auth"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/middleware"
	"github.com/temizlik-delisi/pkg/state"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {

	r.POST("/google-login", GoogleLogin(s))
	r.POST("/apple-login", AppleLogin(s))

	// test endpoint
	r.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"data":   "test",
			"status": 200,
		})
	})

	r.DELETE("/customer", middleware.Authorized(), middleware.AccountType(1), DeleteCustomer(s))
	r.DELETE("/cleaner", middleware.Authorized(), middleware.AccountType(2), DeleteCleaner(s))
}

// @Summary Google Login
// @Description Google Login
// @Tags Auth
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForGoogleLogin true "google login request payload"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /google-login [POST]
func GoogleLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForGoogleLogin
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Google Login Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		resp, err := s.GoogleLoginRegister(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Google Login Error",
				Message:   "Google Login Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_login", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Google Login",
			Message:     "Google Login Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Apple Login
// @Description Apple Sign-In Login
// @Tags Auth
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForAppleLogin true "apple login request payload"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /apple-login [POST]
func AppleLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForAppleLogin
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Apple Login Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		response, err := s.AppleLoginRegister(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Apple Login Error",
				Message:   "Apple Login Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_login", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Apple Login",
			Message:     "Apple Login Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   response,
			"status": 200,
		})
	}
}

// @Summary Delete Customer
// @Description Delete Customer
// @Tags Auth
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 201 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /delete-customer [DELETE]
func DeleteCustomer(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.DeleteCustomer(c); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Customer Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Customer",
			Message:     "Customer Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

// @Summary Delete Cleaner
// @Description Delete Cleaner
// @Tags Auth
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 201 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /delete-cleaner [DELETE]
func DeleteCleaner(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.DeleteCleaner(c); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Cleaner Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Cleaner",
			Message:     "Cleaner Deleted Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}
