package cleaner

import (
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/verification"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Verify TC
// @Description Verify TC
// @Tags Verification Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForVerifyTC true "request payload for verify tc"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /verification/tc-no [POST]
func VerifyTC(s verification.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForVerifyTC
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Verify TC Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: state.GetCurrentID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		if err := s.VerifyTC(c, req.TCNo); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Verify TC Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: state.GetCurrentID(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_process", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Verify TC",
			Message:     "TC Verified Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   localizer.GetTranslated("success_process", state.GetCurrentPhoneLanguage(c), nil),
			"status": 200,
		})
	}
}

// @Summary Upload Criminal Record Document
// @Description Upload Criminal Record Document
// @Tags Cleaner-CriminalRecordDocument
// @Security BearerAuth
// @Accept  multipart/form-data
// @Produce  json
// @Param criminal_record_file formData file true "Criminal record document file"
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /cleaner/criminal-record [POST]
func UploadCriminalRecordDocument(s verification.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.ControlCriminalRecordDocument(c); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Upload Criminal Record Document Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "criminal_record_document_already_uploaded" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("criminal_record_document_already_uploaded", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}

			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		file, header, err := c.Request.FormFile("criminal_record_file")
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Upload Criminal Record Document - Form File Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		defer file.Close()

		// Validate file extension
		allowedExtensions := []string{".pdf"}
		ext := strings.ToLower(filepath.Ext(header.Filename))
		if !isAllowedExtension(ext, allowedExtensions) {
			mainlog.CreateLog(&entities.Log{
				Title:       "Upload Criminal Record Document - Invalid Extension",
				Message:     "Invalid file extension: " + ext,
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_invalid_file_type", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		// Generate unique filename
		filename := strings.ReplaceAll(filepath.Base(header.Filename), " ", "")
		filename = "criminal-record-" + state.GetCurrentID(c).String() + "-" + uuid.NewString() + ext

		// Get content type
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			switch ext {
			case ".pdf":
				contentType = "application/pdf"
			default:
				contentType = "application/octet-stream"
			}
		}

		// Upload file using service
		if err := s.UploadCriminalRecordDocument(c, file, filename, header.Size, contentType); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Upload Criminal Record Document Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			if err.Error() == "criminal_record_document_already_uploaded" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("criminal_record_document_already_uploaded", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_process", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Upload Criminal Record Document",
			Message:     "Criminal record document uploaded successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   localizer.GetTranslated("success_process", state.GetCurrentPhoneLanguage(c), nil),
			"status": 200,
		})
	}
}

// @Summary Delete Criminal Record Document
// @Description Delete Criminal Record Document
// @Tags Cleaner-CriminalRecordDocument
// @Security BearerAuth
// @Produce  json
// @Success 201 {object} dtos.GetResponseStatusCreated
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /verification/criminal-record [DELETE]
func DeleteCriminalRecordDocument(s verification.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.DeleteCriminalRecordDocument(c); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Criminal Record Document Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: state.GetCurrentID(c),
			})
			if err.Error() == "criminal_record_document_not_found" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("criminal_record_document_not_found", state.GetCurrentPhoneLanguage(c), nil),
					"status": 400,
				})
				return
			}
			c.AbortWithStatusJSON(500, gin.H{
				"error":  localizer.GetTranslated("error_delete", state.GetCurrentPhoneLanguage(c), nil),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Delete Criminal Record Document",
			Message:     "Criminal record document deleted successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("success_delete", state.GetCurrentPhoneLanguage(c), nil),
			"status": 201,
		})
	}
}

func isAllowedExtension(ext string, allowed []string) bool {
	for _, a := range allowed {
		if ext == a {
			return true
		}
	}
	return false
}
