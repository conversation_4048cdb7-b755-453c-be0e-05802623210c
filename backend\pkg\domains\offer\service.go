package offer

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	GetOffersByOrderID(ctx context.Context, page, per_page int, order_id uuid.UUID) (*dtos.PaginatedData, error)
	GetOfferByID(ctx context.Context, id uuid.UUID) (*entities.OfferResponse, error)
	GetMyOffers(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	CreateOffer(ctx context.Context, req dtos.RequestForCreateOffer) error
	DeleteOffer(ctx context.Context, id uuid.UUID) error
	UpdateOffer(ctx context.Context, req dtos.RequestForUpdateOffer) error
	AcceptOffer(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetOffersByOrderID(ctx context.Context, page, per_page int, order_id uuid.UUID) (*dtos.PaginatedData, error) {
	return s.repository.getOffersByOrderID(ctx, page, per_page, order_id)
}

func (s *service) GetOfferByID(ctx context.Context, id uuid.UUID) (*entities.OfferResponse, error) {
	return s.repository.getOfferByID(ctx, id)
}

func (s *service) GetMyOffers(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.getMyOffers(ctx, page, per_page)
}

func (s *service) CreateOffer(ctx context.Context, req dtos.RequestForCreateOffer) error {
	return s.repository.createOffer(ctx, req)
}

func (s *service) DeleteOffer(ctx context.Context, id uuid.UUID) error {
	return s.repository.deleteOffer(ctx, id)
}

func (s *service) UpdateOffer(ctx context.Context, req dtos.RequestForUpdateOffer) error {
	return s.repository.updateOffer(ctx, req)
}

func (s *service) AcceptOffer(ctx context.Context, id uuid.UUID) error {
	return s.repository.acceptOffer(ctx, id)
}
