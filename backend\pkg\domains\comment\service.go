package comment

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type Service interface {
	CreateComment(ctx context.Context, req dtos.RequestForCreateComment) error
	GetComments(ctx context.Context, account_id string, page, per_page int) (*dtos.PaginatedData, error)
	DeleteComment(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateComment(ctx context.Context, req dtos.RequestForCreateComment) error {
	return s.repository.CreateComment(ctx, req)
}

func (s *service) GetComments(ctx context.Context, account_id string, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.GetComments(ctx, account_id, page, per_page)
}

func (s *service) DeleteComment(ctx context.Context, id uuid.UUID) error {
	return s.repository.DeleteComment(ctx, id)
}
