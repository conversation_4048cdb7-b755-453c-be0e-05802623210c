package dtos

type RequestForCreateBlog struct {
	Title    string `json:"title" validate:"required" example:"Blog Title"`
	Content  string `json:"content" validate:"required" example:"Blog content here..."`
	Summary  string `json:"summary" validate:"required" example:"Short summary of the blog"`
	ImageURL string `json:"image_url" example:"https://example.com/image.jpg"`
	Tags     string `json:"tags" example:"cleaning,tips,home"`
}

type RequestForUpdateBlog struct {
	ID       string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title    string `json:"title" validate:"required" example:"Blog Title"`
	Content  string `json:"content" validate:"required" example:"Blog content here..."`
	Summary  string `json:"summary" validate:"required" example:"Short summary of the blog"`
	ImageURL string `json:"image_url" example:"https://example.com/image.jpg"`
	IsActive bool   `json:"is_status" validate:"required" example:"true"`
	Tags     string `json:"tags" example:"cleaning,tips,home"`
}

type RequestForDeleteBlog struct {
	ID string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
