package cmd

import (
	"log"

	"github.com/temizlik-delisi/pkg/cache"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/cron"
	"github.com/temizlik-delisi/pkg/database"
	"github.com/temizlik-delisi/pkg/dummy"
	"github.com/temizlik-delisi/pkg/server"
	"github.com/temizlik-delisi/pkg/utils"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	cache.InitRedis(config.Redis)

	// Initialize file storage directories based on environment
	if err := utils.InitializeDirectories(); err != nil {
		log.Printf("Warning: Failed to initialize storage directories: %v", err)
	}

	cron.MyCron()
	dummy.CreateDummy()
	server.LaunchHttpServer(config.App, config.Allows)
}
