package entities

import (
	"time"

	"github.com/google/uuid"
)

type License struct {
	Base

	Type               int        `json:"type" example:"1"`         // 1. free 2. gold 3. premium
	OrderCount         int        `json:"order_count" example:"10"` // free lisence: (cleaner: 2, customer: 1), gold and plat unlimited
	AdRate             int        `json:"ad_rate" example:"10"`     // 1. full 2. half 3. none
	DoSeeEveryCategory bool       `json:"do_see_every_category" gorm:"default:false" example:"false"`
	AccountID          uuid.UUID  `json:"account_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType        int        `json:"account_type" example:"1"`
	EndDate            *time.Time `json:"end_date" example:"2021-01-01 00:00:00"`
}

type LicenseResponse struct {
	ID                 string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt          string `json:"created_at" example:"2021-01-01 00:00:00"`
	Type               int    `json:"type" example:"1"`
	OrderCount         int    `json:"order_count" example:"2"`
	AdRate             int    `json:"ad_rate" example:"10"`
	DoSeeEveryCategory bool   `json:"do_see_every_category" gorm:"default:false" example:"false"`
	AccountID          string `json:"account_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType        int    `json:"account_type" example:"1"`
}

func (l *License) Response() LicenseResponse {
	var resp LicenseResponse

	resp.ID = l.ID.String()
	resp.CreatedAt = l.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Type = l.Type
	resp.OrderCount = l.OrderCount
	resp.AdRate = l.AdRate
	resp.DoSeeEveryCategory = l.DoSeeEveryCategory
	resp.AccountID = l.AccountID.String()
	resp.AccountType = l.AccountType

	return resp
}
