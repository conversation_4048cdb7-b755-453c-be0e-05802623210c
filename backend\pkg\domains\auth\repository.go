package auth

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/temizlik-delisi/pkg/cache"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"github.com/temizlik-delisi/pkg/utils"

	"gorm.io/gorm"
)

type Repository interface {
	GoogleLoginForCustomer(ctx context.Context, req dtos.RequestForGoogleLogin, googleInfo *dtos.GoogleUserInfo) (*entities.Customer, error)
	GoogleLoginForCleaner(ctx context.Context, req dtos.RequestForGoogleLogin, googleInfo *dtos.GoogleUserInfo) (*entities.Cleaner, error)

	AppleLoginForCustomer(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Customer, error)
	AppleLoginForCleaner(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Cleaner, error)

	deleteCustomer(ctx context.Context) error
	deleteCleaner(ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GoogleLoginForCustomer(ctx context.Context, req dtos.RequestForGoogleLogin, googleInfo *dtos.GoogleUserInfo) (*entities.Customer, error) {

	if !googleInfo.VerifiedEmail {
		return nil, fmt.Errorf("google account email is not verified")
	}

	// Önce Google ID ile kullanıcı ara
	customer, err := r.findCustomerByProviderID(ctx, googleInfo.ID, "google")
	if err == nil {
		// Kullanıcı bulundu, bilgilerini güncelle
		customer.LastVersionName = req.LastVersionName
		customer.LastVersionBuildNumber = req.LastVersionBuildNumber
		customer.PushNotifToken = req.PushNotifToken
		customer.Os = req.Os
		customer.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(customer)
		return customer, nil
	}

	// Google ID ile bulunamadı, email ile ara
	customer, err = r.findCustomerByEmail(ctx, googleInfo.Email)
	if err == nil {
		// Email ile bulundu, Google ID'yi ekle
		customer.GoogleID = &googleInfo.ID
		customer.Provider = "google"
		customer.LastVersionName = req.LastVersionName
		customer.LastVersionBuildNumber = req.LastVersionBuildNumber
		customer.PushNotifToken = req.PushNotifToken
		customer.Os = req.Os
		customer.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(customer)
		return customer, nil
	}

	return r.createGoogleCustomer(ctx, req, googleInfo)
}

func (r *repository) GoogleLoginForCleaner(ctx context.Context, req dtos.RequestForGoogleLogin, userInfo *dtos.GoogleUserInfo) (*entities.Cleaner, error) {

	if !userInfo.VerifiedEmail {
		return nil, fmt.Errorf("google account email is not verified")
	}

	// Önce Google ID ile kullanıcı ara
	cleaner, err := r.findCleanerByProviderID(ctx, userInfo.ID, "google")
	if err == nil {
		// Kullanıcı bulundu, bilgilerini güncelle
		cleaner.LastVersionName = req.LastVersionName
		cleaner.LastVersionBuildNumber = req.LastVersionBuildNumber
		cleaner.PushNotifToken = req.PushNotifToken
		cleaner.Os = req.Os
		cleaner.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(cleaner)
		return cleaner, nil
	}

	// Google ID ile bulunamadı, email ile ara
	cleaner, err = r.findCleanerByEmail(ctx, userInfo.Email)
	if err == nil {
		// Email ile bulundu, Google ID'yi ekle
		cleaner.GoogleID = &userInfo.ID
		cleaner.Provider = "google"
		cleaner.LastVersionName = req.LastVersionName
		cleaner.LastVersionBuildNumber = req.LastVersionBuildNumber
		cleaner.PushNotifToken = req.PushNotifToken
		cleaner.Os = req.Os
		cleaner.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(cleaner)
		return cleaner, nil
	}

	return r.createGoogleCleaner(ctx, req, userInfo)
}

func (r *repository) findCustomerByProviderID(ctx context.Context, id string, provider string) (*entities.Customer, error) {
	var customer entities.Customer
	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where(provider+"_id = ?", id).
		First(&customer).Error
	return &customer, err
}

func (r *repository) findCleanerByProviderID(ctx context.Context, id string, provider string) (*entities.Cleaner, error) {
	var cleaner entities.Cleaner
	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where(provider+"_id = ?", id).
		First(&cleaner).Error
	return &cleaner, err
}

func (r *repository) findCustomerByEmail(ctx context.Context, email string) (*entities.Customer, error) {
	var customer entities.Customer
	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("email = ?", email).
		First(&customer).Error
	return &customer, err
}

func (r *repository) findCleanerByEmail(ctx context.Context, email string) (*entities.Cleaner, error) {
	var cleaner entities.Cleaner
	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("email = ?", email).
		First(&cleaner).Error
	return &cleaner, err
}

func (r *repository) createGoogleCustomer(ctx context.Context, req dtos.RequestForGoogleLogin, googleInfo *dtos.GoogleUserInfo) (*entities.Customer, error) {
	ref_code, _ := utils.GenerateReferenceCode()
	customer := &entities.Customer{
		BaseInfo: entities.BaseInfo{
			Name:                   googleInfo.GivenName,
			Surname:                googleInfo.FamilyName,
			Email:                  googleInfo.Email,
			GoogleID:               &googleInfo.ID,
			Provider:               "google",
			TimeZone:               req.TimeZone,
			PhoneLanguage:          req.PhoneLanguage,
			Os:                     req.Os,
			PurchaseID:             req.PurchaseID,
			PushNotifToken:         req.PushNotifToken,
			LastVersionName:        req.LastVersionName,
			LastVersionBuildNumber: req.LastVersionBuildNumber,
			LastLoginDate:          time.Now(),
			ReferenceID:            ref_code,
		},
	}

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).Create(customer).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	var license entities.License

	license.Type = 1
	license.AdRate = 1
	license.DoSeeEveryCategory = false
	license.AccountID = customer.ID
	license.AccountType = 1
	license.OrderCount = 1
	license.EndDate = nil

	if err := tx.WithContext(ctx).Create(&license).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return customer, nil
}

func (r *repository) createGoogleCleaner(ctx context.Context, req dtos.RequestForGoogleLogin, googleInfo *dtos.GoogleUserInfo) (*entities.Cleaner, error) {
	ref_code, _ := utils.GenerateReferenceCode()
	cleaner := &entities.Cleaner{
		BaseInfo: entities.BaseInfo{
			Name:                   googleInfo.GivenName,
			Surname:                googleInfo.FamilyName,
			Email:                  googleInfo.Email,
			GoogleID:               &googleInfo.ID,
			Provider:               "google",
			TimeZone:               req.TimeZone,
			PhoneLanguage:          req.PhoneLanguage,
			Os:                     req.Os,
			PurchaseID:             req.PurchaseID,
			PushNotifToken:         req.PushNotifToken,
			LastVersionName:        req.LastVersionName,
			LastVersionBuildNumber: req.LastVersionBuildNumber,
			LastLoginDate:          time.Now(),
			ReferenceID:            ref_code,
		},
	}

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).Create(cleaner).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	var license entities.License

	license.Type = 1
	license.AdRate = 1
	license.DoSeeEveryCategory = false
	license.AccountID = cleaner.ID
	license.AccountType = 2
	license.OrderCount = 2
	license.EndDate = nil

	if err := tx.WithContext(ctx).Create(&license).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return cleaner, nil
}

func (r *repository) AppleLoginForCustomer(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Customer, error) {

	if !appleInfo.VerifiedEmail {
		return nil, fmt.Errorf("apple account email is not verified")
	}

	// Önce Apple ID ile kullanıcı ara
	customer, err := r.findCustomerByProviderID(ctx, appleInfo.ID, "apple")
	if err == nil {
		// Kullanıcı bulundu, bilgilerini güncelle
		customer.LastVersionName = req.LastVersionName
		customer.LastVersionBuildNumber = req.LastVersionBuildNumber
		customer.PushNotifToken = req.PushNotifToken
		customer.Os = req.Os
		customer.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(customer)
		return customer, nil
	}

	// Apple ID ile bulunamadı, email ile ara
	customer, err = r.findCustomerByEmail(ctx, appleInfo.Email)
	if err == nil {
		// Email ile bulundu, Apple ID'yi ekle
		customer.AppleID = &appleInfo.ID
		customer.Provider = "apple"
		customer.LastVersionName = req.LastVersionName
		customer.LastVersionBuildNumber = req.LastVersionBuildNumber
		customer.PushNotifToken = req.PushNotifToken
		customer.Os = req.Os
		customer.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(customer)
		return customer, nil
	}

	return r.createAppleCustomer(ctx, req, appleInfo)
}

func (r *repository) AppleLoginForCleaner(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Cleaner, error) {
	if !appleInfo.VerifiedEmail {
		return nil, fmt.Errorf("apple account email is not verified")
	}
	// Önce Apple ID ile kullanıcı ara
	cleaner, err := r.findCleanerByProviderID(ctx, appleInfo.ID, "apple")
	if err == nil {
		// Kullanıcı bulundu, bilgilerini güncelle
		cleaner.LastVersionName = req.LastVersionName
		cleaner.LastVersionBuildNumber = req.LastVersionBuildNumber
		cleaner.PushNotifToken = req.PushNotifToken
		cleaner.Os = req.Os
		cleaner.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(cleaner)
		return cleaner, nil
	}
	// Apple ID ile bulunamadı, email ile ara
	cleaner, err = r.findCleanerByEmail(ctx, appleInfo.Email)
	if err == nil {
		// Email ile bulundu, Apple ID'yi ekle
		cleaner.AppleID = &appleInfo.ID
		cleaner.Provider = "apple"
		cleaner.LastVersionName = req.LastVersionName
		cleaner.LastVersionBuildNumber = req.LastVersionBuildNumber
		cleaner.PushNotifToken = req.PushNotifToken
		cleaner.Os = req.Os
		cleaner.LastLoginDate = time.Now()

		r.db.WithContext(ctx).Save(cleaner)
		return cleaner, nil
	}

	return r.createAppleCleaner(ctx, req, appleInfo)
}

func (r *repository) createAppleCustomer(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Customer, error) {
	ref_code, _ := utils.GenerateReferenceCode()
	customer := &entities.Customer{
		BaseInfo: entities.BaseInfo{
			Name:                   appleInfo.GivenName,
			Surname:                appleInfo.FamilyName,
			Email:                  appleInfo.Email,
			AppleID:                &appleInfo.ID,
			Provider:               "apple",
			TimeZone:               req.TimeZone,
			PhoneLanguage:          req.PhoneLanguage,
			Os:                     req.Os,
			PurchaseID:             req.PurchaseID,
			PushNotifToken:         req.PushNotifToken,
			LastVersionName:        req.LastVersionName,
			LastVersionBuildNumber: req.LastVersionBuildNumber,
			LastLoginDate:          time.Now(),
			ReferenceID:            ref_code,
		},
	}

	err := r.db.WithContext(ctx).Create(customer).Error
	if err != nil {
		return nil, err
	}

	return customer, nil
}

func (r *repository) createAppleCleaner(ctx context.Context, req dtos.RequestForAppleLogin, appleInfo *dtos.AppleUserInfo) (*entities.Cleaner, error) {
	ref_code, _ := utils.GenerateReferenceCode()
	cleaner := &entities.Cleaner{
		BaseInfo: entities.BaseInfo{
			Name:                   appleInfo.GivenName,
			Surname:                appleInfo.FamilyName,
			Email:                  appleInfo.Email,
			AppleID:                &appleInfo.ID,
			Provider:               "apple",
			TimeZone:               req.TimeZone,
			PhoneLanguage:          req.PhoneLanguage,
			Os:                     req.Os,
			PurchaseID:             req.PurchaseID,
			PushNotifToken:         req.PushNotifToken,
			LastVersionName:        req.LastVersionName,
			LastVersionBuildNumber: req.LastVersionBuildNumber,
			LastLoginDate:          time.Now(),
			ReferenceID:            ref_code,
		},
	}

	err := r.db.WithContext(ctx).Create(cleaner).Error
	if err != nil {
		return nil, err
	}

	return cleaner, nil
}

func (r *repository) deleteCustomer(ctx context.Context) error {
	var current_customer entities.Customer

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&current_customer).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(consts.NotFoundCustomer)
		}
		return err
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Address{}).
		Where("user_id = ?", current_customer.ID.String()).
		Delete(&entities.Address{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.WithContext(ctx).
		Where("customer_id = ?", current_customer.ID.String()).
		Delete(&entities.CustomerPreference{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.WithContext(ctx).
		Where("id = ?", current_customer.ID.String()).
		Delete(&entities.Customer{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := cache.RemoveAccountSession(ctx, current_customer.ID.String(), "customer"); err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) deleteCleaner(ctx context.Context) error {
	var current_cleaner entities.Cleaner

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&current_cleaner).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(consts.NotFoundCleaner)
		}
		return err
	}

	if err := tx.WithContext(ctx).
		Where("id = ?", current_cleaner.ID.String()).
		Delete(&entities.Cleaner{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := cache.RemoveAccountSession(ctx, current_cleaner.ID.String(), "cleaner"); err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}
