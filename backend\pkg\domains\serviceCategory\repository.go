package serviceCategory

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	GetAllServiceCategory(ctx context.Context) ([]entities.ServiceCategoryResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetAllServiceCategory(ctx context.Context) ([]entities.ServiceCategoryResponse, error) {
	var (
		main_service_categories []entities.ServiceCategory
		resp                    []entities.ServiceCategoryResponse
	)

	r.db.WithContext(ctx).
		Model(&entities.ServiceCategory{}).
		Where("approved_by_admin = ?", true).
		Where("is_main = ?", true).
		Find(&main_service_categories)

	for _, v := range main_service_categories {
		var main_resp, sub_resp entities.ServiceCategoryResponse
		main_resp = v.Response()

		var sub_service_categories []entities.ServiceCategory
		r.db.WithContext(ctx).
			Model(&entities.ServiceCategory{}).
			Where("approved_by_admin = ?", true).
			Where("is_main = ?", false).
			Where("main_category_id = ?", v.ID).
			Find(&sub_service_categories)

		for _, sub_v := range sub_service_categories {
			sub_resp = sub_v.Response()
			main_resp.SubCategories = append(main_resp.SubCategories, sub_resp)
		}

		resp = append(resp, main_resp)
	}

	return resp, nil
}
