package dtos

type RequestForCreateOffer struct {
	OrderID      string  `json:"order_id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Description  string  `json:"description" validate:"required" example:"I can clean your house"`
	NewStartDate string  `json:"new_start_date" validate:"required" example:"2021-01-01 00:00:00"`
	NewEndDate   string  `json:"new_end_date" validate:"required" example:"2021-01-01 00:00:00"`
	NewPrice     float64 `json:"new_price" validate:"required" example:"100"`
}

type RequestForUpdateOffer struct {
	ID           string  `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Description  string  `json:"description" validate:"required" example:"I can clean your house"`
	NewStartDate string  `json:"new_start_date" validate:"required" example:"2021-01-01 00:00:00"`
	NewEndDate   string  `json:"new_end_date" validate:"required" example:"2021-01-01 00:00:00"`
	NewPrice     float64 `json:"new_price" validate:"required" example:"100"`
}
