package serviceCategory

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	GetAllServiceCategory(ctx context.Context) ([]entities.ServiceCategoryResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetAllServiceCategory(ctx context.Context) ([]entities.ServiceCategoryResponse, error) {
	return s.repository.GetAllServiceCategory(ctx)
}
