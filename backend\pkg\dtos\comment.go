package dtos

type RequestForCreateComment struct {
	TargetAccountID   string `json:"target_account_id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	TargetAccountType int    `json:"target_account_type" validate:"required" example:"1"`
	JustRate          bool   `json:"just_rate" example:"false"`
	Rating            int    `json:"rating" validate:"required" example:"5"`
	Comment           string `json:"comment" example:"very good"`
}
