package dtos

import (
	"time"

	"github.com/google/uuid"
)

type RequestForCreateOrder struct {
	Title             string    `json:"title" validate:"required"`
	Description       string    `json:"description" validate:"required"`
	StartDate         time.Time `json:"start_date" validate:"required"`
	EndDate           time.Time `json:"end_date" validate:"required"`
	PricePerCleaner   float64   `json:"price_per_cleaner" validate:"required"`
	TotalPrice        float64   `json:"total_price"`
	HowManyCleaners   int       `json:"how_many_cleaners" default:"1"`
	HouseSize         int       `json:"house_size"`
	RoomNumbers       string    `json:"room_number" validate:"required"`
	ServiceCategoryID string    `json:"service_category_id" validate:"required"`
	AddressID         string    `json:"address_id" validate:"required"`
}

type ResponseForCreateOrder struct {
	OrderID uuid.UUID `json:"order_id"`
}

type RequestForUpdateOrder struct {
	ID                string    `json:"id" validate:"required"`
	Title             string    `json:"title"`
	Description       string    `json:"description"`
	StartDate         time.Time `json:"start_date"`
	EndDate           time.Time `json:"end_date"`
	PricePerCleaner   float64   `json:"price_per_cleaner"`
	TotalPrice        float64   `json:"total_price"`
	HowManyCleaners   int       `json:"how_many_cleaners"`
	HouseSize         int       `json:"house_size"`
	RoomNumbers       string    `json:"room_number"`
	ServiceCategoryID string    `json:"service_category_id"`
	AddressID         string    `json:"address_id"`
}
