package dtos

import (
	"time"

	"github.com/google/uuid"
)

type RequestForCreateOrder struct {
	Title             string    `json:"title" validate:"required" example:"Clean my house"`
	Description       string    `json:"description" validate:"required" example:"I need someone to clean my house"`
	StartDate         time.Time `json:"start_date" validate:"required" example:"2021-01-01 00:00:00"`
	EndDate           time.Time `json:"end_date" validate:"required" example:"2021-01-01 00:00:00"`
	PricePerCleaner   float64   `json:"price_per_cleaner" validate:"required" example:"100"`
	TotalPrice        float64   `json:"total_price" validate:"required" example:"100"`
	HowManyCleaners   int       `json:"how_many_cleaners" example:"1"`
	HouseSize         int       `json:"house_size" example:"120"`
	RoomNumbers       string    `json:"room_number" validate:"required" example:"3+1"`
	ServiceCategoryID string    `json:"service_category_id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type ResponseForCreateOrder struct {
	OrderID uuid.UUID `json:"order_id"`
}

type RequestForUpdateOrder struct {
	ID                string    `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title             string    `json:"title" example:"Clean my house"`
	Description       string    `json:"description" example:"I need someone to clean my house"`
	StartDate         time.Time `json:"start_date" example:"2021-01-01 00:00:00"`
	EndDate           time.Time `json:"end_date" example:"2021-01-01 00:00:00"`
	PricePerCleaner   float64   `json:"price_per_cleaner" example:"100"`
	TotalPrice        float64   `json:"total_price" example:"100"`
	HowManyCleaners   int       `json:"how_many_cleaners" example:"1"`
	HouseSize         int       `json:"house_size" example:"120"`
	RoomNumbers       string    `json:"room_number" example:"3+1"`
	ServiceCategoryID string    `json:"service_category_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
