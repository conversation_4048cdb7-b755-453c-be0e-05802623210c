package dtos

type RequestForProfileUpdate struct {
	Name            string `json:"name" example:"<PERSON>"`
	Surname         string `json:"surname" example:"<PERSON><PERSON>"`
	DateOfBirth     string `json:"date_of_birth" example:"1990-01-01" validate:"required"`
	Phone           string `json:"phone" example:"+905321234567" validate:"required"`
	Country         string `json:"country" example:"Turkey" validate:"required"`
	ProfilePhotoURL string `json:"profile_photo_url,omitempty" example:"https://minio.example.com/profile-photos/photo.jpg"`
}
