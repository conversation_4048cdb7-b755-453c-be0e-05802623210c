package entities

import "github.com/google/uuid"

type Blog struct {
	Base

	Title           string    `json:"title" example:"Blog Title"`
	Content         string    `json:"content" example:"Blog content here..."`
	Summary         string    `json:"summary" example:"Short summary of the blog"`
	CoverImageURL   string    `json:"cover_image_url" example:"https://example.com/image.jpg"`
	AuthorID        uuid.UUID `json:"author_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	ApprovedByAdmin bool      `json:"approved_by_admin" gorm:"default:false" example:"false"`
	ViewCount       int       `json:"view_count" gorm:"default:0" example:"0"`
	Tags            string    `json:"tags" example:"cleaning,tips,home"`
}

type BlogResponse struct {
	ID              string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt       string `json:"created_at" example:"2021-01-01 00:00:00"`
	UpdatedAt       string `json:"updated_at" example:"2021-01-01 00:00:00"`
	Title           string `json:"title" example:"Blog Title"`
	Content         string `json:"content" example:"Blog content here..."`
	Summary         string `json:"summary" example:"Short summary of the blog"`
	CoverImageURL   string `json:"cover_image_url" example:"https://example.com/image.jpg"`
	AuthorID        string `json:"author_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	ApprovedByAdmin bool   `json:"approved_by_admin" example:"false"`
	ViewCount       int    `json:"view_count" example:"0"`
	Tags            string `json:"tags" example:"cleaning,tips,home"`
}

func (b *Blog) Response() BlogResponse {
	var resp BlogResponse

	resp.ID = b.ID.String()
	resp.CreatedAt = b.CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = b.UpdatedAt.Format("2006-01-02 15:04:05")
	resp.Title = b.Title
	resp.Content = b.Content
	resp.Summary = b.Summary
	resp.CoverImageURL = b.CoverImageURL
	resp.AuthorID = b.AuthorID.String()
	resp.ApprovedByAdmin = b.ApprovedByAdmin
	resp.ViewCount = b.ViewCount
	resp.Tags = b.Tags

	return resp
}
