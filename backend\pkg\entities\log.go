package entities

import "github.com/google/uuid"

type Log struct {
	Base

	AccountID   uuid.UUID `json:"account_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType int       `json:"account_type" example:"1"` // -----> 1: user, 2: cleaner
	Title       string    `json:"title" example:"example title"`
	Message     string    `json:"message" example:"order created"`
	Entity      string    `json:"entity" example:"order"`
	Type        string    `json:"type" example:"info"`  // -----> info, error
	Proto       string    `json:"proto" example:"http"` // -----> http, grpc
	Ip          string    `json:"ip" example:"127.0.0.1"`
	Url         string    `json:"url" example:"/api/v1/orders"`
	OS          string    `json:"os" example:"ios"`
}
