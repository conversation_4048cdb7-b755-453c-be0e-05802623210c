package entities

import (
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Address struct {
	Base

	AccountID   uuid.UUID `json:"account_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType int       `json:"account_type" example:"1"` // -----> 1: client, 2: cleaner
	Title       string    `json:"title" example:"home"`
	Line1       string    `json:"line1" example:"Cumhuriyet Mah."`
	Line2       string    `json:"line2" example:"Cumhuriyet Cd."`
	Line3       string    `json:"line3" example:"Cumhuriyet Sk."`
	City        string    `json:"city" example:"Istanbul"`
	District    string    `json:"district" example:"Bakirkoy"`
	Zip         string    `json:"zip" example:"34000"`
	Country     string    `json:"country" example:"Turkey"`
	Lat         float64   `json:"lat" example:"41.0082"`
	Lng         float64   `json:"lng" example:"28.9784"`
	IsMain      bool      `json:"is_main" gorm:"default:false" example:"false"`

	// PostGIS location alanı
	Location string `json:"-" gorm:"type:geography(POINT,4326)"`
}

func (a *Address) BeforeCreate(tx *gorm.DB) (err error) {
	if a.Lat != 0 && a.Lng != 0 {
		a.Location = fmt.Sprintf("SRID=4326;POINT(%f %f)", a.Lng, a.Lat)
	}
	return
}

func (a *Address) BeforeUpdate(tx *gorm.DB) (err error) {
	if a.Lat != 0 && a.Lng != 0 {
		a.Location = fmt.Sprintf("SRID=4326;POINT(%f %f)", a.Lng, a.Lat)
	}
	return
}

type AddressResponse struct {
	ID          string  `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt   string  `json:"created_at" example:"2021-01-01 00:00:00"`
	AccountType int     `json:"account_type" example:"1"`
	AccountID   string  `json:"account_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title       string  `json:"title" example:"home"`
	Line1       string  `json:"line1" example:"Cumhuriyet Mah."`
	Line2       string  `json:"line2" example:"Cumhuriyet Cd."`
	Line3       string  `json:"line3" example:"Cumhuriyet Sk."`
	City        string  `json:"city" example:"Istanbul"`
	District    string  `json:"district" example:"Bakirkoy"`
	Zip         string  `json:"zip" example:"34000"`
	Country     string  `json:"country" example:"Turkey"`
	Lat         float64 `json:"lat" example:"41.0082"`
	Lng         float64 `json:"lng" example:"28.9784"`
	IsMain      bool    `json:"is_main" gorm:"default:false" example:"false"`
}

func (a *Address) Response() AddressResponse {
	var resp AddressResponse

	resp.ID = a.ID.String()
	resp.CreatedAt = a.CreatedAt.Format("2006-01-02 15:04:05")
	resp.AccountType = a.AccountType
	resp.AccountID = a.AccountID.String()
	resp.Title = a.Title
	resp.Line1 = a.Line1
	resp.Line2 = a.Line2
	resp.Line3 = a.Line3
	resp.City = a.City
	resp.District = a.District
	resp.Zip = a.Zip
	resp.Country = a.Country
	resp.Lat = a.Lat
	resp.Lng = a.Lng
	resp.IsMain = a.IsMain

	return resp
}
