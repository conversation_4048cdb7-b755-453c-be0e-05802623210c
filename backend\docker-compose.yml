version: "3"
services:
  temizlik-delisi-db:
    image: "postgis/postgis:14-3.3"
    container_name: temizlik-delisi-db
    volumes:
      -  temizlik_delisi_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "127.0.0.1:5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}

  temizlik-delisi:
    image: temizlikdelisi/temizlik-delisi:${IMAGE_TAG}
    container_name: temizlik-delisi
    restart: always
    networks:
      - main
    volumes:
      - ./config.yaml:/config.yaml
      - ./uploads:/app/uploads
    ports:
      - 8000:8000
    depends_on:
      -  temizlik-delisi-db
      -  temizlik-delisi-redis

  temizlik-delisi-redis:
    image: "redis:latest"
    container_name: temizlik-delisi-redis
    networks:
      - main
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

volumes:
  temizlik_delisi_data:

networks:
  main:
    name: main_network
    driver: bridge