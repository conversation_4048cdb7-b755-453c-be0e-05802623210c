basePath: /api/v1
definitions:
  dtos.GetLanguagesResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/dtos.ResponseForLanguage'
        type: array
      status:
        example: 200
        type: integer
    type: object
  dtos.GetResponseStatusBadRequest:
    properties:
      error:
        example: Bad Request
        type: string
      status:
        example: 400
        type: integer
    type: object
  dtos.GetResponseStatusCreated:
    properties:
      data:
        example: .. created or deleted successfully
        type: string
      status:
        example: 201
        type: integer
    type: object
  dtos.GetResponseStatusInternalServerError:
    properties:
      error:
        example: Internal Server Error
        type: string
      status:
        example: 500
        type: integer
    type: object
  dtos.GetWorkExperienceResponse:
    properties:
      data:
        $ref: '#/definitions/dtos.ResponseForWorkExperience'
      status:
        example: 200
        type: integer
    type: object
  dtos.GetWorkExperiencesResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/dtos.ResponseForWorkExperience'
        type: array
      status:
        example: 200
        type: integer
    type: object
  dtos.PaginateResponseForSwagger:
    properties:
      data:
        $ref: '#/definitions/dtos.PaginatedData'
      status:
        example: 200
        type: integer
    type: object
  dtos.PaginatedData:
    properties:
      is_last_page:
        example: false
        type: boolean
      page:
        example: 1
        type: integer
      per_page:
        example: 10
        type: integer
      rows:
        items:
          type: object
        type: array
      total:
        example: 100
        type: integer
      total_pages:
        example: 10
        type: integer
    type: object
  dtos.RequestForAppleLogin:
    properties:
      account_type:
        description: 1. Normal User 2. Cleaner
        example: 1
        type: integer
      apple_code:
        example: c123456789.0.abcd.efgh
        type: string
      last_version_build_number:
        example: 3
        type: integer
      last_version_name:
        example: 1.0.0
        type: string
      os:
        example: ios
        type: string
      phone_language:
        example: tr
        type: string
      purchase_id:
        example: "**********"
        type: string
      push_notif_token:
        example: "**********"
        type: string
      time_zone:
        example: Europe/Istanbul
        type: string
    required:
    - account_type
    - apple_code
    - os
    - phone_language
    - purchase_id
    - push_notif_token
    - time_zone
    type: object
  dtos.RequestForCreateAddress:
    properties:
      city:
        example: Istanbul
        type: string
      country:
        example: Turkey
        type: string
      district:
        example: Bakirkoy
        type: string
      is_main:
        example: false
        type: boolean
      lat:
        example: 41.0082
        type: number
      line1:
        example: Cumhuriyet Mah.
        type: string
      line2:
        example: Cumhuriyet Cd.
        type: string
      line3:
        example: Cumhuriyet Sk.
        type: string
      lng:
        example: 28.9784
        type: number
      title:
        example: home
        type: string
      zip:
        example: "34000"
        type: string
    required:
    - city
    - country
    type: object
  dtos.RequestForCreateBlog:
    properties:
      content:
        example: Blog content here...
        type: string
      image_url:
        example: https://example.com/image.jpg
        type: string
      summary:
        example: Short summary of the blog
        type: string
      tags:
        example: cleaning,tips,home
        type: string
      title:
        example: Blog Title
        type: string
    required:
    - content
    - summary
    - title
    type: object
  dtos.RequestForCreateComment:
    properties:
      comment:
        example: very good
        type: string
      just_rate:
        example: false
        type: boolean
      rating:
        example: 5
        type: integer
      target_account_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      target_account_type:
        example: 1
        type: integer
    required:
    - rating
    - target_account_id
    - target_account_type
    type: object
  dtos.RequestForCreateCustomerPreference:
    properties:
      preference_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - preference_id
    type: object
  dtos.RequestForCreateLanguage:
    properties:
      code:
        example: TR
        type: string
      level:
        description: basic, conversational, fluent, native
        example: conversational
        type: string
      name:
        example: Turkish
        type: string
    required:
    - code
    - level
    - name
    type: object
  dtos.RequestForCreateOrder:
    properties:
      description:
        example: I need someone to clean my house
        type: string
      end_date:
        example: "2021-01-01 00:00:00"
        type: string
      house_size:
        example: 120
        type: integer
      how_many_cleaners:
        example: 1
        type: integer
      price_per_cleaner:
        example: 100
        type: number
      room_number:
        example: 3+1
        type: string
      service_category_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      start_date:
        example: "2021-01-01 00:00:00"
        type: string
      title:
        example: Clean my house
        type: string
      total_price:
        example: 100
        type: number
    required:
    - description
    - end_date
    - price_per_cleaner
    - room_number
    - service_category_id
    - start_date
    - title
    - total_price
    type: object
  dtos.RequestForCreatePreference:
    properties:
      description:
        example: "true"
        type: string
      name:
        example: ironing
        type: string
    required:
    - name
    type: object
  dtos.RequestForCreateServiceCategory:
    properties:
      description:
        example: standart home cleaning description
        type: string
      is_main:
        example: false
        type: boolean
      main_category_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      min_duration:
        example: 60
        type: integer
      service_name_en:
        example: standart home cleaning
        type: string
      service_name_tr:
        example: standart home cleaning
        type: string
    required:
    - description
    - min_duration
    - service_name_en
    - service_name_tr
    type: object
  dtos.RequestForCreateWorkExperience:
    properties:
      company:
        example: ABC Cleaning Services
        type: string
      country:
        example: Turkey
        type: string
      current:
        example: false
        type: boolean
      description:
        example: Responsible for residential cleaning services
        type: string
      end_date:
        example: "2023-12-31"
        type: string
      location:
        example: Istanbul
        type: string
      start_date:
        example: "2020-01-01"
        type: string
      title:
        example: Senior Cleaner
        type: string
    required:
    - company
    - country
    - location
    - start_date
    - title
    type: object
  dtos.RequestForDeleteBlog:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - id
    type: object
  dtos.RequestForDeletePreference:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - id
    type: object
  dtos.RequestForDeleteServiceCategory:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - id
    type: object
  dtos.RequestForGoogleLogin:
    properties:
      account_type:
        description: 1. Client 2. Cleaner
        example: 1
        type: integer
      google_id_token:
        example: ya29.a0AfH6SMC...
        type: string
      last_version_build_number:
        example: 3
        type: integer
      last_version_name:
        example: 1.0.0
        type: string
      os:
        example: android
        type: string
      phone_language:
        example: tr
        type: string
      purchase_id:
        example: "**********"
        type: string
      push_notif_token:
        example: "**********"
        type: string
      time_zone:
        example: Europe/Istanbul
        type: string
    required:
    - account_type
    - google_id_token
    - os
    - phone_language
    - purchase_id
    - push_notif_token
    - time_zone
    type: object
  dtos.RequestForNewVersion:
    properties:
      android_build_number:
        type: integer
      android_force_update_build_number:
        type: integer
      android_version_name:
        type: string
      ios_build_number:
        type: integer
      ios_force_update_build_number:
        type: integer
      ios_version_name:
        type: string
      is_force:
        type: boolean
    type: object
  dtos.RequestForUpdateAddress:
    properties:
      city:
        example: Istanbul
        type: string
      country:
        example: Turkey
        type: string
      district:
        example: Bakirkoy
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_main:
        example: false
        type: boolean
      lat:
        example: 41.0082
        type: number
      line1:
        example: Cumhuriyet Mah.
        type: string
      line2:
        example: Cumhuriyet Cd.
        type: string
      line3:
        example: Cumhuriyet Sk.
        type: string
      lng:
        example: 28.9784
        type: number
      title:
        example: home
        type: string
      zip:
        example: "34000"
        type: string
    required:
    - city
    - country
    - id
    type: object
  dtos.RequestForUpdateBlog:
    properties:
      content:
        example: Blog content here...
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      image_url:
        example: https://example.com/image.jpg
        type: string
      is_status:
        example: true
        type: boolean
      summary:
        example: Short summary of the blog
        type: string
      tags:
        example: cleaning,tips,home
        type: string
      title:
        example: Blog Title
        type: string
    required:
    - content
    - id
    - is_status
    - summary
    - title
    type: object
  dtos.RequestForUpdateOrder:
    properties:
      description:
        example: I need someone to clean my house
        type: string
      end_date:
        example: "2021-01-01 00:00:00"
        type: string
      house_size:
        example: 120
        type: integer
      how_many_cleaners:
        example: 1
        type: integer
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      price_per_cleaner:
        example: 100
        type: number
      room_number:
        example: 3+1
        type: string
      service_category_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      start_date:
        example: "2021-01-01 00:00:00"
        type: string
      title:
        example: Clean my house
        type: string
      total_price:
        example: 100
        type: number
    required:
    - id
    type: object
  dtos.RequestForUpdatePreference:
    properties:
      description:
        example: "true"
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_active:
        example: true
        type: boolean
      name:
        example: ironing
        type: string
    required:
    - id
    - is_active
    - name
    type: object
  dtos.RequestForUpdateServiceCategory:
    properties:
      description:
        example: standart home cleaning description
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_status:
        example: true
        type: boolean
      min_duration:
        example: 60
        type: integer
      service_name_en:
        example: standart home cleaning
        type: string
      service_name_tr:
        example: standart home cleaning
        type: string
    required:
    - description
    - id
    - is_status
    - min_duration
    - service_name_en
    - service_name_tr
    type: object
  dtos.RequestForUpdateWorkExperience:
    properties:
      company:
        example: ABC Cleaning Services
        type: string
      country:
        example: Turkey
        type: string
      current:
        example: false
        type: boolean
      description:
        example: Responsible for residential cleaning services
        type: string
      end_date:
        example: "2023-12-31"
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      location:
        example: Istanbul
        type: string
      start_date:
        example: "2020-01-01"
        type: string
      title:
        example: Senior Cleaner
        type: string
    required:
    - company
    - country
    - id
    - location
    - start_date
    - title
    type: object
  dtos.RequestForVerifyTC:
    properties:
      tc_no:
        example: "**********1"
        type: string
    required:
    - tc_no
    type: object
  dtos.ResponseForLanguage:
    properties:
      cleaner_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      code:
        example: TR
        type: string
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      level:
        description: basic, conversational, fluent, native
        example: conversational
        type: string
      name:
        example: Turkish
        type: string
    type: object
  dtos.ResponseForWorkExperience:
    properties:
      cleaner_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      company:
        example: ABC Cleaning Services
        type: string
      country:
        example: Turkey
        type: string
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      current:
        example: false
        type: boolean
      description:
        example: Responsible for residential cleaning services
        type: string
      end_date:
        example: "2023-12-31"
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      location:
        example: Istanbul
        type: string
      start_date:
        example: "2020-01-01"
        type: string
      title:
        example: Senior Cleaner
        type: string
    type: object
  entities.AddressResponse:
    properties:
      account_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      account_type:
        example: 1
        type: integer
      city:
        example: Istanbul
        type: string
      country:
        example: Turkey
        type: string
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      district:
        example: Bakirkoy
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_main:
        example: false
        type: boolean
      lat:
        example: 41.0082
        type: number
      line1:
        example: Cumhuriyet Mah.
        type: string
      line2:
        example: Cumhuriyet Cd.
        type: string
      line3:
        example: Cumhuriyet Sk.
        type: string
      lng:
        example: 28.9784
        type: number
      title:
        example: home
        type: string
      zip:
        example: "34000"
        type: string
    type: object
  entities.OrderResponse:
    properties:
      account:
        $ref: '#/definitions/entities.UserResponse'
      address:
        $ref: '#/definitions/entities.AddressResponse'
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      description:
        type: string
      distance:
        type: number
      end_date:
        type: string
      house_size:
        type: integer
      how_many_cleaners:
        type: integer
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      price_per_cleaner:
        type: number
      service_category:
        $ref: '#/definitions/entities.ServiceCategoryResponse'
      start_date:
        type: string
      title:
        type: string
      total_price:
        type: number
    type: object
  entities.ResponseForDetail:
    properties:
      account_type:
        example: 1
        type: integer
      apple_id:
        example: "**********"
        type: string
      country:
        example: Turkey
        type: string
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      date_of_birth:
        example: "1990-01-01"
        type: string
      email:
        example: <EMAIL>
        type: string
      google_id:
        example: "**********"
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      is_profile_updated:
        example: true
        type: boolean
      last_login_date:
        example: "2021-01-01 00:00:00"
        type: string
      last_version_build_number:
        example: 3
        type: integer
      last_version_name:
        example: 1.0.0
        type: string
      name:
        example: John
        type: string
      os:
        example: android
        type: string
      phone:
        example: "**********"
        type: string
      phone_language:
        example: tr
        type: string
      profile_photo_url:
        example: https://minio.example.com/profile-photos/photo.jpg
        type: string
      provider:
        example: google
        type: string
      purchase_id:
        example: "**********"
        type: string
      push_notif_token:
        example: "**********"
        type: string
      reference_id:
        example: ABC45678
        type: string
      surname:
        example: Doe
        type: string
      time_zone:
        example: Europe/Istanbul
        type: string
    type: object
  entities.ServiceCategoryResponse:
    properties:
      approved_by_admin:
        example: false
        type: boolean
      created_at:
        example: "2021-01-01 00:00:00"
        type: string
      description:
        example: standart home cleaning description
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      image_url:
        example: https://example.com/image.jpg
        type: string
      is_main:
        example: false
        type: boolean
      min_duration:
        example: 60
        type: integer
      permitted_lisance_types:
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        type: array
      service_name_en:
        example: standart home cleaning
        type: string
      service_name_tr:
        example: standart home cleaning
        type: string
      sub_categories:
        items:
          $ref: '#/definitions/entities.ServiceCategoryResponse'
        type: array
    type: object
  entities.UserResponse:
    properties:
      account_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      account_type:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      email:
        example: <EMAIL>
        type: string
      name:
        example: John
        type: string
      surname:
        example: Doe
        type: string
    type: object
host: **************:8000
info:
  contact: {}
  description: Temizlik Delisi API Documentation
  title: Temizlik Delisi API
  version: "1.0"
paths:
  /admin/blog/create:
    post:
      consumes:
      - application/json
      description: Create Blog
      parameters:
      - description: request payload for create blog
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateBlog'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Create Blog
      tags:
      - Admin Endpoints
  /admin/blog/delete:
    post:
      consumes:
      - application/json
      description: Delete Blog
      parameters:
      - description: request payload for delete blog
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeleteBlog'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Delete Blog
      tags:
      - Admin Endpoints
  /admin/blog/get-all:
    get:
      description: Get All Blog
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      - description: 'approved number 1: approved, 2: not approved, if you send any
          other number, it will return all blogs'
        in: query
        name: approved
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Blog
      tags:
      - Admin Endpoints
  /admin/blog/get-by-id:
    get:
      description: Get Blog By ID
      parameters:
      - description: blog id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Blog By ID
      tags:
      - Admin Endpoints
  /admin/blog/update:
    post:
      consumes:
      - application/json
      description: Update Blog
      parameters:
      - description: request payload for update blog
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateBlog'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Blog
      tags:
      - Admin Endpoints
  /admin/comment/delete:
    post:
      consumes:
      - application/json
      description: Delete Comment
      parameters:
      - description: request payload for delete comment
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeleteServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Delete Comment
      tags:
      - Admin Endpoints
  /admin/comment/get-all:
    get:
      description: Get All Comment
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      - description: 'approved number 1: approved, 2: not approved, if you send any
          other number, it will return all comments'
        in: query
        name: approved
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Comment
      tags:
      - Admin Endpoints
  /admin/order/delete:
    post:
      consumes:
      - application/json
      description: Delete Order
      parameters:
      - description: request payload for delete order
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeleteServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Delete Order
      tags:
      - Admin Endpoints
  /admin/order/get-all:
    post:
      description: Get All Order
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Order
      tags:
      - Admin Endpoints
  /admin/order/get-by-id:
    post:
      description: Get Order By ID
      parameters:
      - description: order id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Order By ID
      tags:
      - Admin Endpoints
  /admin/preference/create:
    post:
      consumes:
      - application/json
      description: Create Preference
      parameters:
      - description: request payload for create preference
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreatePreference'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Create Preference
      tags:
      - Admin Endpoints
  /admin/preference/delete:
    post:
      consumes:
      - application/json
      description: Delete Preference
      parameters:
      - description: request payload for delete preference
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeletePreference'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Delete Preference
      tags:
      - Admin Endpoints
  /admin/preference/get-all:
    post:
      description: Get All Preference
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      - description: 'approved number 1: approved, 2: not approved, if you send any
          other number, it will return all preferences'
        in: query
        name: approved
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Preference
      tags:
      - Admin Endpoints
  /admin/preference/get-by-id:
    post:
      description: Get Preference By ID
      parameters:
      - description: preference id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Preference By ID
      tags:
      - Admin Endpoints
  /admin/preference/update:
    post:
      consumes:
      - application/json
      description: Update Preference
      parameters:
      - description: request payload for update preference
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdatePreference'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Preference
      tags:
      - Admin Endpoints
  /admin/service-category/create:
    post:
      consumes:
      - application/json
      description: Create Service Category
      parameters:
      - description: request payload for create service category
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Create Service Category
      tags:
      - Admin Endpoints
  /admin/service-category/delete:
    post:
      consumes:
      - application/json
      description: Delete Service Category
      parameters:
      - description: request payload for delete service category
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeleteServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Delete Service Category
      tags:
      - Admin Endpoints
  /admin/service-category/get-all:
    get:
      description: Get All Service Category
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      - description: 'approved number 1: approved, 2: not approved, if you send any
          other number, it will return all service types'
        in: query
        name: approved
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Service Category
      tags:
      - Admin Endpoints
  /admin/service-category/update:
    post:
      consumes:
      - application/json
      description: Update Service Category
      parameters:
      - description: request payload for update service category
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Service Category
      tags:
      - Admin Endpoints
  /admin/verification/tc/approved:
    post:
      consumes:
      - application/json
      description: Approved TC
      parameters:
      - description: request payload for approved tc
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDeleteServiceCategory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Approved TC
      tags:
      - Admin Endpoints
  /admin/version/new:
    post:
      consumes:
      - application/json
      description: New Version
      parameters:
      - description: request payload for new version
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForNewVersion'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: New Version
      tags:
      - Admin Endpoints
  /apple-login:
    post:
      consumes:
      - application/json
      description: Apple Sign-In Login
      parameters:
      - description: apple login request payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForAppleLogin'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Apple Login
      tags:
      - Auth
  /cleaner/criminal-record:
    post:
      consumes:
      - multipart/form-data
      description: Upload Criminal Record Document
      parameters:
      - description: Criminal record document file
        in: formData
        name: criminal_record_file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Upload Criminal Record Document
      tags:
      - Cleaner-CriminalRecordDocument
  /cleaner/language:
    get:
      description: Get Languages
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.GetLanguagesResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Languages
      tags:
      - Cleaner-Language
    post:
      consumes:
      - application/json
      description: Create Language
      parameters:
      - description: request payload for create language
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateLanguage'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Create Language
      tags:
      - Cleaner-Language
  /cleaner/language/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Language
      parameters:
      - description: language id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Language
      tags:
      - Cleaner-Language
  /cleaner/work-experience:
    get:
      description: Get Work Experiences
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.GetWorkExperiencesResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Work Experiences
      tags:
      - Cleaner-WorkExperience
    post:
      consumes:
      - application/json
      description: Create Work Experience
      parameters:
      - description: Work Experience data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateWorkExperience'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Create Work Experience
      tags:
      - Cleaner-WorkExperience
  /cleaner/work-experience/{id}:
    delete:
      description: Delete Work Experience
      parameters:
      - description: Work Experience ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Work Experience
      tags:
      - Cleaner-WorkExperience
    get:
      description: Get Work Experience by ID
      parameters:
      - description: Work Experience ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.GetWorkExperienceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Work Experience
      tags:
      - Cleaner-WorkExperience
    put:
      consumes:
      - application/json
      description: Update Work Experience
      parameters:
      - description: Work Experience data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateWorkExperience'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Update Work Experience
      tags:
      - Cleaner-WorkExperience
  /common/address:
    get:
      description: Get Addresses
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.PaginateResponseForSwagger'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Addresses
      tags:
      - Common-Address
    post:
      consumes:
      - application/json
      description: Create Address
      parameters:
      - description: request payload for create address
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateAddress'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Create Address
      tags:
      - Common-Address
  /common/address/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Address
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Address
      tags:
      - Common-Address
    get:
      description: Get Address By ID
      parameters:
      - description: address id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entities.AddressResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Address By ID
      tags:
      - Common-Address
    patch:
      consumes:
      - application/json
      description: Update Address
      parameters:
      - description: request payload for update address
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateAddress'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Update Address
      tags:
      - Common-Address
  /common/blog:
    get:
      description: Get All Blog
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get All Blog
      tags:
      - Blog
  /common/blog/{id}:
    get:
      description: Get Blog By ID
      parameters:
      - description: blog id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Blog By ID
      tags:
      - Blog
  /common/comment:
    get:
      description: Get Comments
      parameters:
      - description: if you send an account_id, it will return comments for that account,
          if you don't send an account_id, it will return comments for the current
          user
        in: query
        name: account_id
        type: string
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.PaginatedData'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Comments
      tags:
      - Common-Comment
    post:
      consumes:
      - application/json
      description: Create Comment
      parameters:
      - description: create comment request payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateComment'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Create Comment
      tags:
      - Common-Comment
  /common/comment/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Comment
      parameters:
      - description: comment id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Comment
      tags:
      - Common-Comment
  /common/license:
    get:
      consumes:
      - application/json
      description: Get License
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get License
      tags:
      - License
  /common/my-order:
    get:
      description: Get My Orders
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.PaginateResponseForSwagger'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get My Orders
      tags:
      - Common-Order
  /common/my-profile:
    get:
      description: Get Customer Profile
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entities.ResponseForDetail'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Customer Profile
      tags:
      - Common-Profile
  /common/order:
    get:
      description: Get Orders
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      - description: radius meters number
        in: query
        name: radius_meters
        required: true
        type: integer
      - description: account type number if you don't send an account_type, it will
          return orders for the opposite account type
        in: query
        name: account_type
        required: true
        type: integer
      - description: min range number
        in: query
        name: min_range
        type: integer
      - description: max range number
        in: query
        name: max_range
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.PaginateResponseForSwagger'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Orders
      tags:
      - Common-Order
    post:
      consumes:
      - application/json
      description: Create Order
      parameters:
      - description: request payload for create order
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateOrder'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Create Order
      tags:
      - Common-Order
  /common/order/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Order
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Order
      tags:
      - Common-Order
    get:
      description: Get Order By ID
      parameters:
      - description: order id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entities.OrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Order By ID
      tags:
      - Common-Order
    patch:
      consumes:
      - application/json
      description: Update Order
      parameters:
      - description: request payload for update order
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateOrder'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Update Order
      tags:
      - Common-Order
  /common/profile:
    post:
      consumes:
      - multipart/form-data
      description: Update Customer Profile
      parameters:
      - description: Date of birth
        example: '"1990-01-01"'
        in: formData
        name: date_of_birth
        required: true
        type: string
      - description: Phone number
        example: '"+905321234567"'
        in: formData
        name: phone
        required: true
        type: string
      - description: Country
        example: '"Turkey"'
        in: formData
        name: country
        required: true
        type: string
      - description: Name
        example: '"John"'
        in: formData
        name: name
        required: true
        type: string
      - description: Surname
        example: '"Doe"'
        in: formData
        name: surname
        required: true
        type: string
      - description: Profile photo
        in: formData
        name: profile_photo
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Update Customer Profile
      tags:
      - Common-Profile
  /common/profile/{id}:
    get:
      description: Get Profile By ID
      parameters:
      - description: profile id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/entities.ResponseForDetail'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Get Profile By ID
      tags:
      - Common-Profile
  /common/service-category:
    get:
      description: Get All Service Category
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/entities.ServiceCategoryResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - none: []
      summary: Get All Service Category
      tags:
      - Common-ServiceCategory
  /customer-preference:
    get:
      description: Get All Customer Preference
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get All Customer Preference
      tags:
      - Customer
    post:
      consumes:
      - application/json
      description: Create Customer Preference
      parameters:
      - description: request payload for create customer preference
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateCustomerPreference'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create Customer Preference
      tags:
      - Customer
  /customer-preference/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Customer Preference
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete Customer Preference
      tags:
      - Customer
  /customer-preference/user:
    get:
      description: Get Customer With Preferences
      parameters:
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: per_page number
        in: query
        name: per_page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get Customer With Preferences
      tags:
      - Customer
  /delete-cleaner:
    delete:
      consumes:
      - application/json
      description: Delete Cleaner
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete Cleaner
      tags:
      - Auth
  /delete-customer:
    delete:
      consumes:
      - application/json
      description: Delete Customer
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete Customer
      tags:
      - Auth
  /google-login:
    post:
      consumes:
      - application/json
      description: Google Login
      parameters:
      - description: google login request payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForGoogleLogin'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Google Login
      tags:
      - Auth
  /verification/criminal-record:
    delete:
      description: Delete Criminal Record Document
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusCreated'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusBadRequest'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/dtos.GetResponseStatusInternalServerError'
      security:
      - BearerAuth: []
      summary: Delete Criminal Record Document
      tags:
      - Cleaner-CriminalRecordDocument
  /verification/tc-no:
    post:
      consumes:
      - application/json
      description: Verify TC
      parameters:
      - description: request payload for verify tc
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForVerifyTC'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Verify TC
      tags:
      - Verification Endpoints
  /version:
    get:
      description: Get Version
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get Version
      tags:
      - Version Endpoints
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
