package admin

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/domains/admin"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/middleware"
	"github.com/temizlik-delisi/pkg/state"
)

func AdminRoutes(r *gin.RouterGroup, s admin.Service) {
	a := r.Group("/admin")
	a.Use(middleware.FromAdmin())
	{
		a.POST("/service-category/create", CreateServiceCategory(s))
		a.POST("/service-category/delete", DeleteServiceCategory(s))
		a.POST("/service-category/update", UpdateServiceCategory(s))
		a.GET("/service-category/get-all", GetAllServiceCategory(s))

		a.GET("/comment/get-all", GetAllComment(s))
		a.POST("/comment/delete", DeleteComment(s))

		a.POST("/blog/create", CreateBlog(s))
		a.POST("/blog/delete", DeleteBlog(s))
		a.POST("/blog/update", UpdateBlog(s))
		a.GET("/blog/get-all", GetAllBlog(s))
		a.GET("/blog/get-by-id", GetBlogByID(s))

		a.POST("/order/get-all", GetAllOrder(s))
		a.POST("/order/get-by-id", GetOrderByID(s))
		a.POST("/order/delete", DeleteOrder(s))

		a.POST("/preference/create", CreatePreference(s))
		a.POST("/preference/update", UpdatePreference(s))
		a.POST("/preference/delete", DeletePreference(s))
		a.POST("/preference/get-all", GetAllPreference(s))
		a.POST("/preference/get-by-id", GetPreferenceByID(s))

		a.POST("/version/new", NewVersion(s))

		a.POST("/verification/tc/approved", VerificationApprovedTC(s))
	}
}

// @Summary Create Service Category
// @Description Create Service Category
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateServiceCategory true "request payload for create service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/create [POST]
func CreateServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.ServiceCategoryCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Service Category",
			Message:   "Service Category Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Service Category
// @Description Delete Service Category
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/delete [POST]
func DeleteServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.ServiceCategoryDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Service Category",
			Message:   "Service Category Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Service Category
// @Description Update Service Category
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateServiceCategory true "request payload for update service category"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/update [POST]
func UpdateServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Service Category Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.ServiceCategoryUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Update Service Category",
			Message:   "Service Category Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Service Category Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Service Category
// @Description Get All Service Category
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all service types"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/service-category/get-all [GET]
func GetAllServiceCategory(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		is_main, _ := strconv.ParseBool(c.Query("is_main"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.ServiceCategoryGetAll(c, page, per_page, approved, is_main)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Service Category Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Service Category",
			Message:   "Service Category Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get All Comment
// @Description Get All Comment
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all comments"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/comment/get-all [GET]
func GetAllComment(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.CommentGetAll(c, page, per_page, approved)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Comment Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Comment",
			Message:   "Comment Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete Comment
// @Description Delete Comment
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete comment"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/comment/delete [POST]
func DeleteComment(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.CommentDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Comment",
			Message:   "Comment Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Comment Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Create Blog
// @Description Create Blog
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateBlog true "request payload for create blog"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/create [POST]
func CreateBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Blog Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Blog",
			Message:   "Blog Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Blog
// @Description Delete Blog
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Param payload body dtos.RequestForDeleteBlog true "request payload for delete blog"
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/delete [POST]
func DeleteBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Comment Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Blog",
			Message:   "Blog Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Blog
// @Description Update Blog
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateBlog true "request payload for update blog"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/update [POST]
func UpdateBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdateBlog
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Blog Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.BlogUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:   "Update Blog Error",
				Message: "Error: " + err.Error(),
				Type:    "error",
				Proto:   "http",
				Ip:      state.GetCurrentUserIP(c),
				Url:     c.Request.URL.Path,
				OS:      "web",

				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Update Blog",
			Message:   "Blog Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Blog Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Blog
// @Description Get All Blog
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all blogs"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/get-all [GET]
func GetAllBlog(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		per_page, _ := strconv.Atoi(c.Query("per_page"))
		approved, _ := strconv.Atoi(c.Query("approved"))
		if page == 0 {
			page = 1
		}
		if per_page == 0 {
			per_page = 10
		}

		resp, err := s.BlogGetAll(c, page, per_page, approved)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Blog",
			Message:   "Blog Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Blog By ID
// @Description Get Blog By ID
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param id path string true "blog id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/blog/get-by-id [GET]
func GetBlogByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		parsed_id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Blog Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.BlogGetByID(c, parsed_id)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Blog Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get Blog",
			Message:   "Blog Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get All Order
// @Description Get All Order
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/get-all [POST]
func GetAllOrder(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForPaginate
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Order Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if req.Page == 0 {
			req.Page = 1
		}
		if req.PerPage == 0 {
			req.PerPage = 10
		}
		resp, err := s.OrderGetAll(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Order",
			Message:   "Order Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Order By ID
// @Description Get Order By ID
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param id path string true "order id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/get-by-id [POST]
func GetOrderByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Order Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.OrderGetByID(c, uuid.MustParse(req.ID))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get Order",
			Message:   "Order Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete Order
// @Description Delete Order
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for delete order"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/order/delete [POST]
func DeleteOrder(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Order Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if err := s.OrderDelete(c, uuid.MustParse(req.ID)); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Order Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Order",
			Message:   "Order Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   "Order Deleted Successfully",
			"status": 200,
		})
	}
}

// @Summary Create Preference
// @Description Create Preference
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreatePreference true "request payload for create preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/create [POST]
func CreatePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreatePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceCreate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Create Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Create Preference",
			Message:   "Preference Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Update Preference
// @Description Update Preference
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdatePreference true "request payload for update preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/update [POST]
func UpdatePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForUpdatePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceUpdate(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Update Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Update Preference",
			Message:   "Preference Updated Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Updated Successfully",
			"status": 201,
		})
	}
}

// @Summary Delete Preference
// @Description Delete Preference
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeletePreference true "request payload for delete preference"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/delete [POST]
func DeletePreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeletePreference
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.PreferenceDelete(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Delete Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Delete Preference",
			Message:   "Preference Deleted Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "Preference Deleted Successfully",
			"status": 201,
		})
	}
}

// @Summary Get All Preference
// @Description Get All Preference
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param page query int true "page number"
// @Param per_page query int true "per_page number"
// @Param approved query int true "approved number 1: approved, 2: not approved, if you send any other number, it will return all preferences"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/get-all [POST]
func GetAllPreference(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForPaginate
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Preference Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		if req.Page == 0 {
			req.Page = 1
		}
		if req.PerPage == 0 {
			req.PerPage = 10
		}
		resp, err := s.PreferenceGetAll(c, req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get All Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Get All Preference",
			Message:   "Preference Get All Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Preference By ID
// @Description Get Preference By ID
// @Tags Admin Endpoints
// @Security none
// @Produce  json
// @Param id path string true "preference id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/preference/get-by-id [POST]
func GetPreferenceByID(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Preference Parse UUID Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.PreferenceGetByID(c, uuid.MustParse(req.ID))
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Preference Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}
		mainlog.CreateLog(&entities.Log{
			Title:     "Get Preference",
			Message:   "Preference Get Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary New Version
// @Description New Version
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForNewVersion true "request payload for new version"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/version/new [POST]
func NewVersion(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForNewVersion
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "New Version Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.NewVersion(c, req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "New Version Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "New Version",
			Message:   "New Version Created Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "New Version Created Successfully",
			"status": 201,
		})
	}
}

// @Summary Approved TC
// @Description Approved TC
// @Tags Admin Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForDeleteServiceCategory true "request payload for approved tc"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /admin/verification/tc/approved [POST]
func VerificationApprovedTC(s admin.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForDeleteServiceCategory
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Approved TC Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		if err := s.VerificationApprovedTC(c, uuid.MustParse(req.ID)); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Approved TC Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        "web",
				AccountID: uuid.Nil,
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:     "Approved TC",
			Message:   "TC Approved for" + req.ID + " Successfully",
			Type:      "info",
			Proto:     "http",
			Ip:        state.GetCurrentUserIP(c),
			Url:       c.Request.URL.Path,
			OS:        "web",
			AccountID: uuid.Nil,
		})

		c.JSON(201, gin.H{
			"data":   "TC Approved Successfully",
			"status": 201,
		})
	}
}
