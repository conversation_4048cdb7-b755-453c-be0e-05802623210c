package verification

import (
	"context"
	"mime/multipart"
)

type Service interface {
	VerifyTC(ctx context.Context, tc string) error
	UploadCriminalRecordDocument(ctx context.Context, file multipart.File, filename string, fileSize int64, contentType string) error
	ControlCriminalRecordDocument(ctx context.Context) error
	DeleteCriminalRecordDocument(ctx context.Context) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) VerifyTC(ctx context.Context, tc string) error {
	return s.repository.verifyTC(ctx, tc)
}

func (s *service) UploadCriminalRecordDocument(ctx context.Context, file multipart.File, filename string, fileSize int64, contentType string) error {
	return s.repository.uploadCriminalRecordDocument(ctx, file, filename, fileSize, contentType)
}

func (s *service) ControlCriminalRecordDocument(ctx context.Context) error {
	return s.repository.controlCriminalRecordDocument(ctx)
}

func (s *service) DeleteCriminalRecordDocument(ctx context.Context) error {
	return s.repository.deleteCriminalRecordDocument(ctx)
}
