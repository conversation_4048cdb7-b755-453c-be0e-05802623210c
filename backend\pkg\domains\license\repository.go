package license

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	getLicense(ctx context.Context) (entities.LicenseResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getLicense(ctx context.Context) (entities.LicenseResponse, error) {
	var (
		license entities.License
		resp    entities.LicenseResponse
	)
	if err := r.db.WithContext(ctx).
		Model(&entities.License{}).
		Where("account_id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx)).
		Order("created_at desc").
		First(&license).Error; err != nil {
		return entities.LicenseResponse{}, err
	}

	resp = license.Response()

	return resp, nil
}
