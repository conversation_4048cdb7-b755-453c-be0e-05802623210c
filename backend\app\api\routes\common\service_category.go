package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/serviceCategory"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Get All Service Category
// @Description Get All Service Category
// @Tags Common-ServiceCategory
// @Security none
// @Produce  json
// @Success 200 {object} []entities.ServiceCategoryResponse
// @Failure 400 {object} dtos.GetResponseStatusBadRequest
// @Failure 500 {object} dtos.GetResponseStatusInternalServerError
// @Router /common/service-category [GET]
func GetAllServiceCategory(s serviceCategory.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetAllServiceCategory(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get All Service Category Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(500, gin.H{
				"error":  err.Error(),
				"status": 500,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get All Service Category",
			Message:     "Service Category Get All Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
