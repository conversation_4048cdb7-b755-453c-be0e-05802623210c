package entities

import "github.com/google/uuid"

type CustomerPreference struct {
	Base

	CustomerID   uuid.UUID `json:"customer_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PreferenceID uuid.UUID `json:"preference_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

// UserPreferenceResponse - Standard response struct
type CustomerPreferenceResponse struct {
	ID           string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt    string `json:"created_at" example:"2021-01-01 00:00:00"`
	CustomerID   string `json:"customer_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PreferenceID string `json:"preference_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

// UserWithPreferences - User with their preferences
type CustomerWithCustomerPreferences struct {
	ID        string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name      string `json:"name" example:"John"`
	Surname   string `json:"surname" example:"Doe"`
	Email     string `json:"email" example:"<EMAIL>"`
	CreatedAt string `json:"created_at" example:"2021-01-01 00:00:00"`

	Preferences []PreferenceInfo `json:"preferences"`
}

// PreferenceInfo - Simplified preference info for joins
type PreferenceInfo struct {
	ID          string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name        string `json:"name" example:"ironing"`
	Description string `json:"description" example:"Ironing service"`
}

func (u *CustomerPreference) Response() CustomerPreferenceResponse {
	var resp CustomerPreferenceResponse

	resp.ID = u.ID.String()
	resp.CreatedAt = u.CreatedAt.Format("2006-01-02 15:04:05")
	resp.CustomerID = u.CustomerID.String()
	resp.PreferenceID = u.PreferenceID.String()

	return resp
}
