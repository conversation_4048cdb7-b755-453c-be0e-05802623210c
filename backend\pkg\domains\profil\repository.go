package profil

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	updateCustomerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error
	updateCleanerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error

	getMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error)
	getMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error)

	getProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) updateCustomerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error {
	var (
		customer entities.Customer
	)
	r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&customer)

	if customer.ID == uuid.Nil {
		return errors.New(consts.NotFoundCustomer)
	}

	customer.Name = req.Name
	customer.Surname = req.Surname
	customer.DateOfBirth = req.DateOfBirth
	customer.Phone = req.Phone
	customer.Country = req.Country
	customer.IsProfileUpdated = true
	if req.ProfilePhotoURL != "" {
		customer.ProfilePhotoURL = req.ProfilePhotoURL
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Save(&customer).Error

	return err
}

func (r *repository) updateCleanerProfile(ctx context.Context, req dtos.RequestForProfileUpdate) error {
	var (
		cleaner entities.Cleaner
	)
	r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Where("account_type = ?", state.GetCurrentAccountType(ctx)).
		First(&cleaner)

	if cleaner.ID == uuid.Nil {
		return errors.New(consts.NotFoundCleaner)
	}

	cleaner.Name = req.Name
	cleaner.Surname = req.Surname
	cleaner.DateOfBirth = req.DateOfBirth
	cleaner.Phone = req.Phone
	cleaner.Country = req.Country
	cleaner.IsProfileUpdated = true
	if req.ProfilePhotoURL != "" {
		cleaner.ProfilePhotoURL = req.ProfilePhotoURL
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		Save(&cleaner).Error

	return err
}

func (r *repository) getMyCustomerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	var (
		customer entities.Customer
		resp     entities.ResponseForDetail
	)
	err := r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&customer).Error

	resp = customer.ResponseForDetailCustomer()

	return resp, err
}

func (r *repository) getMyCleanerProfile(ctx context.Context) (entities.ResponseForDetail, error) {
	var (
		cleaner entities.Cleaner
		resp    entities.ResponseForDetail
	)
	err := r.db.WithContext(ctx).
		Model(&entities.Cleaner{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&cleaner).Error

	resp = cleaner.ResponseForDetailCleaner()

	return resp, err
}

func (r *repository) getProfileByID(ctx context.Context, account_type int, id uuid.UUID) (entities.ResponseForDetail, error) {
	var (
		customer entities.Customer
		cleaner  entities.Cleaner
		resp     entities.ResponseForDetail
	)

	switch account_type {
	case 1:
		err := r.db.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", id).
			First(&customer).Error
		if err != nil {
			return entities.ResponseForDetail{}, err
		}
		resp = customer.ResponseForDetailCustomer()
		resp.GoogleID = nil
		resp.AppleID = nil
		resp.PurchaseID = ""
		resp.PushNotifToken = ""
	case 2:
		err := r.db.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", id).
			First(&cleaner).Error
		if err != nil {
			return entities.ResponseForDetail{}, err
		}
		resp = cleaner.ResponseForDetailCleaner()
		resp.GoogleID = nil
		resp.AppleID = nil
		resp.PurchaseID = ""
		resp.PushNotifToken = ""
	default:
		return entities.ResponseForDetail{}, errors.New("not_found_account_type")
	}

	return resp, nil
}
