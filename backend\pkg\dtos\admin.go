package dtos

type RequestForCreateServiceCategory struct {
	ServiceNameTR  string `json:"service_name_tr" validate:"required" example:"standart home cleaning"`
	ServiceNameEN  string `json:"service_name_en" validate:"required" example:"standart home cleaning"`
	Description    string `json:"description" validate:"required" example:"standart home cleaning description"`
	MinDuration    int    `json:"min_duration" validate:"required" example:"60"`
	IsMain         bool   `json:"is_main" example:"false"`
	MainCategoryID string `json:"main_category_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type RequestForUpdateServiceCategory struct {
	ID            string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	ServiceNameTR string `json:"service_name_tr" validate:"required" example:"standart home cleaning"`
	ServiceNameEN string `json:"service_name_en" validate:"required" example:"standart home cleaning"`
	Description string `json:"description" validate:"required" example:"standart home cleaning description"`
	MinDuration int    `json:"min_duration" validate:"required" example:"60"`
	IsActive    bool   `json:"is_status" validate:"required" example:"true"`
}

type RequestForDeleteServiceCategory struct {
	ID string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type RequestForPaginate struct {
	Page        int `json:"page" example:"1"`
	PerPage     int `json:"per_page" example:"10"`
	AccountType int `json:"account_type" example:"1"`
	Approved    int `json:"approved" example:"1"`
}
