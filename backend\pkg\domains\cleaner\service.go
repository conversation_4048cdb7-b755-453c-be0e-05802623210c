package cleaner

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type Service interface {
	GetLanguages(ctx context.Context) ([]dtos.ResponseForLanguage, error)
	CreateLanguage(ctx context.Context, req dtos.RequestForCreateLanguage) error
	DeleteLanguage(ctx context.Context, id uuid.UUID) error

	GetWorkExperiences(ctx context.Context) ([]dtos.ResponseForWorkExperience, error)
	GetWorkExperience(ctx context.Context, id uuid.UUID) (dtos.ResponseForWorkExperience, error)
	CreateWorkExperience(ctx context.Context, req dtos.RequestForCreateWorkExperience) error
	UpdateWorkExperience(ctx context.Context, req dtos.RequestForUpdateWorkExperience) error
	DeleteWorkExperience(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetLanguages(ctx context.Context) ([]dtos.ResponseForLanguage, error) {
	return s.repository.GetLanguages(ctx)
}

func (s *service) CreateLanguage(ctx context.Context, req dtos.RequestForCreateLanguage) error {
	return s.repository.CreateLanguage(ctx, req)
}

func (s *service) DeleteLanguage(ctx context.Context, id uuid.UUID) error {
	return s.repository.DeleteLanguage(ctx, id)
}

func (s *service) GetWorkExperiences(ctx context.Context) ([]dtos.ResponseForWorkExperience, error) {
	return s.repository.GetWorkExperiences(ctx)
}

func (s *service) GetWorkExperience(ctx context.Context, id uuid.UUID) (dtos.ResponseForWorkExperience, error) {
	return s.repository.GetWorkExperience(ctx, id)
}

func (s *service) CreateWorkExperience(ctx context.Context, req dtos.RequestForCreateWorkExperience) error {
	return s.repository.CreateWorkExperience(ctx, req)
}

func (s *service) UpdateWorkExperience(ctx context.Context, req dtos.RequestForUpdateWorkExperience) error {
	return s.repository.UpdateWorkExperience(ctx, req)
}

func (s *service) DeleteWorkExperience(ctx context.Context, id uuid.UUID) error {
	return s.repository.DeleteWorkExperience(ctx, id)
}
