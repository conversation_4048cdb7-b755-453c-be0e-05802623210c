package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/license"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Get License
// @Description Get License
// @Tags License
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/license [GET]
func GetLicense(s license.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetLicense(c)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:       "Get License Bind JSON Error",
				Message:     "Error: " + err.Error(),
				Type:        "error",
				Proto:       "http",
				Ip:          state.GetCurrentUserIP(c),
				Url:         c.Request.URL.Path,
				OS:          state.GetCurrentOS(c),
				AccountID:   state.GetCurrentID(c),
				AccountType: state.GetCurrentAccountType(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		mainlog.CreateLog(&entities.Log{
			Title:       "Get License",
			Message:     "License Get Successfully",
			Type:        "info",
			Proto:       "http",
			Ip:          state.GetCurrentUserIP(c),
			Url:         c.Request.URL.Path,
			OS:          state.GetCurrentOS(c),
			AccountID:   state.GetCurrentID(c),
			AccountType: state.GetCurrentAccountType(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
