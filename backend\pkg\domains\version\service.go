package version

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	GetVersion(ctx context.Context) (entities.Version, error)
}
type service struct {
	repository Respository
}

func NewService(repository Respository) Service {
	return &service{
		repository: repository,
	}
}

func (s *service) GetVersion(ctx context.Context) (entities.Version, error) {
	return s.repository.getVersion(ctx)
}
