package blog

import (
	"context"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	getAllBlogs(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	getBlogByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getAllBlogs(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		blogs []entities.Blog
		resp  []entities.BlogResponse
		count int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("approved_by_admin = ?", true)

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&blogs).Error; err != nil {
		return nil, err
	}

	for _, v := range blogs {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) getBlogByID(ctx context.Context, id uuid.UUID) (*entities.BlogResponse, error) {
	var blog entities.Blog
	err := r.db.WithContext(ctx).
		Model(&entities.Blog{}).
		Where("approved_by_admin = ?", true).
		Where("id = ?", id).
		First(&blog).Error

	resp := blog.Response()

	return &resp, err
}
