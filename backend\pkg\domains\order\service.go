package order

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	CreateOrder(ctx context.Context, req dtos.RequestForCreateOrder) (dtos.ResponseForCreateOrder, error)
	DeleteOrder(ctx context.Context, id uuid.UUID) error
	UpdateOrder(ctx context.Context, req dtos.RequestForUpdateOrder) error
	GetOrder(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error)
	GetOrders(ctx context.Context, page, per_page, account_type, account_point int, radius_meters, min_range, max_range float64, start_date, end_date, room_number, order_type string) (*dtos.PaginatedData, error)
	GetMyOrders(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateOrder(ctx context.Context, req dtos.RequestForCreateOrder) (dtos.ResponseForCreateOrder, error) {
	return s.repository.createOrder(ctx, req)
}

func (s *service) DeleteOrder(ctx context.Context, id uuid.UUID) error {
	return s.repository.deleteOrder(ctx, id)
}

func (s *service) UpdateOrder(ctx context.Context, req dtos.RequestForUpdateOrder) error {
	return s.repository.updateOrder(ctx, req)
}

func (s *service) GetOrder(ctx context.Context, id uuid.UUID) (*entities.OrderResponse, error) {
	return s.repository.getByID(ctx, id)
}

func (s *service) GetOrders(ctx context.Context, page, per_page, account_type, account_point int, radius_meters, min_range, max_range float64, start_date, end_date, room_number, order_type string) (*dtos.PaginatedData, error) {
	return s.repository.getOrders(ctx, page, per_page, account_type, account_point, radius_meters, min_range, max_range, start_date, end_date, room_number, order_type)
}

func (s *service) GetMyOrders(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.getMyOrders(ctx, page, per_page)
}
