package comment

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	CreateComment(ctx context.Context, req dtos.RequestForCreateComment) error
	GetComments(ctx context.Context, account_id string, page, per_page int) (*dtos.PaginatedData, error)
	DeleteComment(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateComment(ctx context.Context, req dtos.RequestForCreateComment) error {
	var (
		comment entities.Comment
	)

	var (
		commenter_id uuid.UUID
	)

	if state.GetCurrentAccountType(ctx) == 1 {
		var current_customer entities.Customer
		if err := r.db.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			First(&current_customer).Error; err != nil {
			return err
		}
		commenter_id = current_customer.ID
	} else if state.GetCurrentAccountType(ctx) == 2 {
		var current_cleaner entities.Cleaner
		if err := r.db.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			First(&current_cleaner).Error; err != nil {
			return err
		}
		commenter_id = current_cleaner.ID
	} else {
		return errors.New("not_found_account_type")
	}

	r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("commenter = ? AND who_received_comment = ?", commenter_id, req.TargetAccountID).
		First(&comment)

	// if comment.ID != uuid.Nil {
	// 	return errors.New(consts.AlreadyExistComment)
	// }

	if req.TargetAccountID == state.GetCurrentID(ctx).String() {
		return errors.New("you_cannot_comment_yourself")
	}

	if req.TargetAccountType == state.GetCurrentAccountType(ctx) {
		return errors.New("you_cannot_comment_someone_who_has_same_account_type")
	}

	comment = entities.Comment{
		Commenter:          commenter_id,
		WhoReceivedComment: uuid.MustParse(req.TargetAccountID),
		JustRate:           req.JustRate,
		Rating:             req.Rating,
		Comment:            req.Comment,
		ApprovedByAdmin:    false,
	}

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Model(&entities.Comment{}).
		Create(&comment).Error; err != nil {
		tx.Rollback()
		return err
	}

	switch req.TargetAccountType {
	case 1:
		if err := tx.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", req.TargetAccountID).
			Updates(map[string]interface{}{
				"total_comment": gorm.Expr("total_comment + ?", 1),
				"total_point":   gorm.Expr("total_point + ?", req.Rating),
				"average_rate":  gorm.Expr("(total_point + ?) / (total_comment + 1)", req.Rating),
			}).Error; err != nil {
			tx.Rollback()
			return err
		}
	case 2:
		if err := tx.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", req.TargetAccountID).
			Updates(map[string]interface{}{
				"total_comment": gorm.Expr("total_comment + ?", 1),
				"total_point":   gorm.Expr("total_point + ?", req.Rating),
				"average_rate":  gorm.Expr("(total_point + ?) / (total_comment + 1)", req.Rating),
			}).Error; err != nil {
			tx.Rollback()
			return err
		}
	default:
		return errors.New("not_found_account_type")
	}

	tx.Commit()

	return nil
}

func (r *repository) GetComments(ctx context.Context, account_id string, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		comments []entities.Comment
		resp     []entities.CommentResponse
		count    int64
	)
	base_query := r.db.WithContext(ctx).Debug().
		Model(&entities.Comment{})

	if account_id != "" {
		base_query = base_query.Where("who_received_comment = ?", account_id)
	} else {
		base_query = base_query.Where("who_received_comment = ?", state.GetCurrentID(ctx))
	}

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&comments).Error; err != nil {
		return nil, err
	}

	for _, v := range comments {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) DeleteComment(ctx context.Context, id uuid.UUID) error {
	var comment entities.Comment
	r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Where("id = ?", id).
		First(&comment)

	if comment.ID == uuid.Nil {
		return errors.New(consts.NotFoundComment)
	}

	if comment.Commenter != state.GetCurrentID(ctx) {
		return errors.New("you_cannot_delete_this_comment")
	}

	return r.db.WithContext(ctx).
		Model(&entities.Comment{}).
		Delete(&comment).Error
}
