package database

import (
	"fmt"
	"log"
	"sync"

	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db          *gorm.DB
	err         error
	client_once sync.Once
)

func InitDB(dbc config.Database) {
	client_once.Do(func() {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
		db, err = gorm.Open(
			postgres.New(
				postgres.Config{
					DSN:                  dsn,
					PreferSimpleProtocol: true,
				},
			),
		)
		if err != nil {
			panic(err)
		}

		db.Exec("CREATE EXTENSION IF NOT EXISTS postgis")

		db.AutoMigrate(
			&entities.Log{},
			&entities.Version{},
			&entities.Customer{},
			&entities.Cleaner{},
			&entities.ServiceCategory{},
			&entities.Comment{},
			&entities.Address{},
			&entities.Blog{},
			&entities.Preference{},
			&entities.CustomerPreference{},
			&entities.Order{},
			&entities.License{},
			&entities.Language{},
			&entities.WorkExperience{},
			&entities.CriminalRecordDocument{},
			&entities.Offer{},
		)

		db.Exec("CREATE INDEX IF NOT EXISTS idx_addresses_location ON addresses USING GIST(location)")
	})
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
