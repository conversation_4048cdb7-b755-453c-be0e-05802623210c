package customer

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/customerPreference"
	"github.com/temizlik-delisi/pkg/middleware"
)

func CustomerRoutes(r *gin.RouterGroup, s customerPreference.Service) {
	c := r.Group("/customer")

	c.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1))
	{
		c.POST("/preference", CreateCustomerPreference(s))
		c.DELETE("/preference/:id", DeleteCustomerPreference(s))
		c.GET("/preference", GetAllCustomerPreference(s))

		c.GET("/preference/customer", GetCustomerWithPreferences(s))
	}
}
