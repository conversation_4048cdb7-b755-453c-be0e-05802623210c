package customerPreference

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type Service interface {
	CustomerPreferenceCreate(ctx context.Context, req dtos.RequestForCreateCustomerPreference) error
	CustomerPreferenceDelete(ctx context.Context, id uuid.UUID) error
	CustomerPreferenceGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)

	// Join methods
	GetCustomerWithPreferences(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CustomerPreferenceCreate(ctx context.Context, req dtos.RequestForCreateCustomerPreference) error {
	return s.repository.customerPreferenceCreate(ctx, req)
}

func (s *service) CustomerPreferenceDelete(ctx context.Context, id uuid.UUID) error {
	return s.repository.customerPreferenceDelete(ctx, id)
}

func (s *service) CustomerPreferenceGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.customerPreferenceGetAll(ctx, page, per_page)
}

// Join methods implementation
func (s *service) GetCustomerWithPreferences(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.getCustomerWithPreferences(ctx, page, per_page)
}
