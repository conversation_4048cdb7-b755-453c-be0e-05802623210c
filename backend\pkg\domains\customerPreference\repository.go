package customerPreference

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	customerPreferenceCreate(ctx context.Context, req dtos.RequestForCreateCustomerPreference) error
	customerPreferenceDelete(ctx context.Context, id uuid.UUID) error
	customerPreferenceGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)

	// Join methods
	getCustomerWithPreferences(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) customerPreferenceCreate(ctx context.Context, req dtos.RequestForCreateCustomerPreference) error {
	var customer_preference_control entities.CustomerPreference
	var preference entities.Preference

	r.db.WithContext(ctx).
		Model(&entities.Preference{}).
		Where("id = ?", req.PreferenceID).
		First(&preference)

	if preference.ID == uuid.Nil {
		return errors.New(consts.NotFoundPreference)
	}

	if !preference.ApprovedByAdmin {
		return errors.New(consts.NotFoundPreference)
	}

	r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Where("customer_id = ? AND preference_id = ?", state.GetCurrentID(ctx), req.PreferenceID).
		First(&customer_preference_control)

	if customer_preference_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistService)
	}

	customer_preference := &entities.CustomerPreference{
		CustomerID:   state.GetCurrentID(ctx),
		PreferenceID: uuid.MustParse(req.PreferenceID),
	}

	err := r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Create(customer_preference).Error

	return err
}

func (r *repository) customerPreferenceDelete(ctx context.Context, id uuid.UUID) error {
	var customer_preference_control entities.CustomerPreference

	r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Where("customer_id = ? AND id = ?", state.GetCurrentID(ctx), id.String()).
		First(&customer_preference_control)

	if customer_preference_control.ID == uuid.Nil {
		return errors.New(consts.NotFoundPreference)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Delete(&customer_preference_control).Error

	return err
}

func (r *repository) customerPreferenceGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		customer_preferences []entities.CustomerPreference
		resp                 []entities.CustomerPreferenceResponse
		count                int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{})

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&customer_preferences).Error; err != nil {
		return nil, err
	}

	for _, v := range customer_preferences {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

// Join methods implementation

func (r *repository) getCustomerWithPreferences(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		customer             entities.Customer
		customer_preferences []entities.CustomerPreference
		resp                 []entities.CustomerWithCustomerPreferences
		count                int64
	)

	r.db.WithContext(ctx).
		Model(&entities.Customer{}).
		Where("id = ?", state.GetCurrentID(ctx)).
		First(&customer)

	if customer.ID == uuid.Nil {
		return nil, errors.New(consts.NotFoundCustomer)
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Where("customer_id = ?", customer.ID).
		Count(&count).Error; err != nil {
		return nil, err
	}

	// Get user preferences with preference details (paginated)
	err := r.db.WithContext(ctx).
		Model(&entities.CustomerPreference{}).
		Where("customer_id = ?", customer.ID).
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&customer_preferences).Error

	if err != nil {
		return nil, err
	}

	// Build response for each page
	customer_with_preferences := entities.CustomerWithCustomerPreferences{
		ID:        customer.ID.String(),
		CreatedAt: customer.CreatedAt.Format("2006-01-02 15:04:05"),
		Name:      customer.Name,
		Surname:   customer.Surname,
		Email:     customer.Email,
	}

	for _, v := range customer_preferences {
		var preference entities.Preference
		r.db.WithContext(ctx).
			Model(&entities.Preference{}).
			Where("id = ?", v.PreferenceID).
			First(&preference)

		prefInfo := entities.PreferenceInfo{
			ID:          v.PreferenceID.String(),
			Name:        preference.Name,
			Description: preference.Description,
		}
		customer_with_preferences.Preferences = append(customer_with_preferences.Preferences, prefInfo)
	}

	resp = append(resp, customer_with_preferences)

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}
