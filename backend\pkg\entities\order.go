package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Order struct {
	Base

	Title                 string    `json:"title"`
	Description           string    `json:"description"`
	StartDate             time.Time `json:"start_date"`
	EndDate               time.Time `json:"end_date"`
	PricePerCleaner       float64   `json:"price_per_cleaner"`
	TotalPrice            float64   `json:"total_price"`
	HowManyCleaners       int       `json:"how_many_cleaners"`
	HouseSize             int       `json:"house_size"`
	RoomNumbers           string    `json:"room_numbers"`
	ServiceCategoryMainID uuid.UUID `json:"service_category_main_id" gorm:"type:uuid"`
	ServiceCategoryID     uuid.UUID `json:"service_category_id" gorm:"type:uuid"`
	AddressID             uuid.UUID `json:"address_id" gorm:"type:uuid"`
	AccountID             uuid.UUID `json:"account_id" gorm:"type:uuid"`
	AccountType           int       `json:"account_type"`
	LicenseType           int       `json:"license_type"`
}

type OrderResponse struct {
	ID              string                  `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt       string                  `json:"created_at" example:"2021-01-01 00:00:00"`
	Title           string                  `json:"title"`
	Description     string                  `json:"description"`
	StartDate       string                  `json:"start_date"`
	EndDate         string                  `json:"end_date"`
	PricePerCleaner float64                 `json:"price_per_cleaner"`
	TotalPrice      float64                 `json:"total_price"`
	HowManyCleaners int                     `json:"how_many_cleaners"`
	HouseSize       int                     `json:"house_size"`
	RoomNumbers     string                  `json:"room_numbers"`
	ServiceCategory ServiceCategoryResponse `json:"service_category"`
	Address         AddressResponse         `json:"address"`
	Account         UserResponse            `json:"account"`
	Distance        float64                 `json:"distance"`
}

func (o *Order) Response(db *gorm.DB) OrderResponse {
	var resp OrderResponse

	resp.ID = o.ID.String()
	resp.CreatedAt = o.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Title = o.Title
	resp.Description = o.Description
	resp.StartDate = o.StartDate.Format("2006-01-02 15:04:05")
	resp.EndDate = o.EndDate.Format("2006-01-02 15:04:05")
	resp.PricePerCleaner = o.PricePerCleaner
	resp.TotalPrice = o.TotalPrice
	resp.HowManyCleaners = o.HowManyCleaners
	resp.HouseSize = o.HouseSize
	resp.RoomNumbers = o.RoomNumbers

	var selected_service_category ServiceCategory
	db.Model(&ServiceCategory{}).
		Where("id = ?", o.ServiceCategoryID).
		First(&selected_service_category)

	resp.ServiceCategory = selected_service_category.Response()

	var selected_address Address
	db.Model(&Address{}).
		Where("id = ?", o.AddressID).
		First(&selected_address)

	resp.Address = selected_address.Response()

	var selected_account UserResponse
	if o.AccountType == 1 {
		var customer Customer
		db.Model(&Customer{}).
			Where("id = ?", o.AccountID).
			First(&customer)

		selected_account = UserResponse{
			AccountID:   customer.ID.String(),
			AccountType: "customer",
			Name:        customer.Name,
			Surname:     customer.Surname,
			Email:       customer.Email,
		}
	} else {
		var cleaner Cleaner
		db.Model(&Cleaner{}).
			Where("id = ?", o.AccountID).
			First(&cleaner)

		selected_account = UserResponse{
			AccountID:   cleaner.ID.String(),
			AccountType: "cleaner",
			Name:        cleaner.Name,
			Surname:     cleaner.Surname,
			Email:       cleaner.Email,
		}
	}

	resp.Account = selected_account

	return resp
}
