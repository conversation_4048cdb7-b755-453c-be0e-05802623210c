package entities

import (
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
)

type WorkExperience struct {
	Base

	Title       string    `json:"title" example:"Cleaner"`
	Company     string    `json:"company" example:"ABC"`
	Location    string    `json:"location" example:"Istanbul"`
	Country     string    `json:"country" example:"Turkey"`
	StartDate   string    `json:"start_date" example:"2021-01-01"`
	EndDate     string    `json:"end_date" example:"2021-01-01"`
	Description string    `json:"description" example:"Cleaner"`
	Current     bool      `json:"current" example:"false"`
	CleanerID   uuid.UUID `json:"cleaner_id" gorm:"type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

func (w *WorkExperience) Response() dtos.ResponseForWorkExperience {
	var resp dtos.ResponseForWorkExperience

	resp.ID = w.ID.String()
	resp.CreatedAt = w.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Title = w.Title
	resp.Company = w.Company
	resp.Location = w.Location
	resp.Country = w.Country
	resp.StartDate = w.StartDate
	resp.EndDate = w.EndDate
	resp.Description = w.Description
	resp.Current = w.Current
	resp.CleanerID = w.CleanerID.String()

	return resp
}
