package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/dtos"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

type GoogleOAuth struct {
	ClientID     string
	ClientIDs    []string
	ClientSecret string
	OAuthConfig  *oauth2.Config
}

func NewGoogleOAuth() *GoogleOAuth {
	cfg := config.InitConfig()

	oauthCfg := &oauth2.Config{
		RedirectURL: cfg.Google.RedirectURL, // mobile veya SPA için
		Scopes: []string{
			"https://www.googleapis.com/auth/userinfo.email",
			"https://www.googleapis.com/auth/userinfo.profile",
		},
		Endpoint: google.Endpoint,
	}

	return &GoogleOAuth{
		ClientIDs:   cfg.Google.ClientIDs,
		OAuthConfig: oauthCfg,
	}
}

func (g *GoogleOAuth) ExchangeCodeForToken(code string) (*oauth2.Token, error) {
	ctx := context.Background()
	token, err := g.OAuthConfig.Exchange(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("code exchange failed: %v", err)
	}
	return token, nil
}

// VerifyGoogleToken Google access token'ını doğrular ve kullanıcı bilgilerini döner
func (g *GoogleOAuth) VerifyGoogleToken(accessToken string) (*dtos.GoogleUserInfo, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("google API request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("google API returned status: %d, body: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	var userInfo dtos.GoogleUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		return nil, fmt.Errorf("failed to parse user info: %v", err)
	}

	if !userInfo.VerifiedEmail {
		return nil, fmt.Errorf("google account email is not verified")
	}

	return &userInfo, nil
}

// ValidateGoogleIDToken Google ID token'ını doğrular (alternatif method)
func (g *GoogleOAuth) ValidateGoogleIDToken(idToken string) (*dtos.GoogleUserInfo, error) {
	url := fmt.Sprintf("https://oauth2.googleapis.com/tokeninfo?id_token=%s", idToken)

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("google tokeninfo request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("google tokeninfo returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read tokeninfo response: %v", err)
	}

	var tokenInfo map[string]interface{}
	if err := json.Unmarshal(body, &tokenInfo); err != nil {
		return nil, fmt.Errorf("failed to parse tokeninfo: %v", err)
	}

	// Audience (client_id) kontrolü - birden fazla client_id'yi kontrol et
	aud, ok := tokenInfo["aud"].(string)
	if !ok {
		return nil, fmt.Errorf("audience not found in token")
	}

	// Audience'ın allowed client_id'ler arasında olup olmadığını kontrol et
	validAudience := false
	for _, clientID := range g.ClientIDs {
		if aud == clientID {
			validAudience = true
			log.Println("Valid audience found:", clientID)
			break
		}
	}

	if !validAudience {
		return nil, fmt.Errorf("invalid audience in token: %s", aud)
	}

	userInfo := &dtos.GoogleUserInfo{
		ID:         getStringFromMap(tokenInfo, "sub"),
		Email:      getStringFromMap(tokenInfo, "email"),
		Name:       getStringFromMap(tokenInfo, "name"),
		GivenName:  getStringFromMap(tokenInfo, "given_name"),
		FamilyName: getStringFromMap(tokenInfo, "family_name"),
		Picture:    getStringFromMap(tokenInfo, "picture"),
	}

	// email_verified field'ını güvenli şekilde al - Google'dan string olarak geliyor
	switch v := tokenInfo["email_verified"].(type) {
	case bool:
		userInfo.VerifiedEmail = v
	case string:
		userInfo.VerifiedEmail = v == "true"
	default:
		userInfo.VerifiedEmail = false
	}

	if !userInfo.VerifiedEmail {
		return nil, fmt.Errorf("google account email is not verified")
	}

	return userInfo, nil
}

// Helper functions
func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}
