package dtos

type RequestForCreatePreference struct {
	Name        string `json:"name" validate:"required" example:"ironing"`
	Description string `json:"description" example:"true"`
}

type RequestForUpdatePreference struct {
	ID          string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name        string `json:"name" validate:"required" example:"ironing"`
	Description string `json:"description" example:"true"`
	IsActive    bool   `json:"is_active" validate:"required" example:"true"`
}

type RequestForDeletePreference struct {
	ID string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
