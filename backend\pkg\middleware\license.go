package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/database"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/state"
)

func OrderCountControl() gin.HandlerFunc {
	return func(c *gin.Context) {
		account_type, exists := c.Get(state.CurrentAccountType)
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		account_id, exists := c.Get(state.CurrentUserID)
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		db := database.DBClient()

		var (
			current_license entities.License
			count           int64
		)

		if err := db.Model(&entities.License{}).
			Where("account_id = ?", account_id).
			Order("created_at desc").
			First(&current_license).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_license_not_found", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		if err := db.Model(&entities.Order{}).
			Where("account_id = ?", account_id).
			Where("DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)").
			Count(&count).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": localizer.GetTranslated("error_license_not_found", c.GetString(state.CurrentPhoneLanguage), nil),
			})
			return
		}

		if current_license.Type == 1 {
			if account_type == 1 {
				if current_license.OrderCount <= int(count) {
					c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_limit_order_count", c.GetString(state.CurrentPhoneLanguage), nil)})
					return
				}

			} else if account_type == 2 {
				if current_license.OrderCount <= int(count) {
					c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_limit_order_count", c.GetString(state.CurrentPhoneLanguage), nil)})
					return
				}
			}
		} else {
			c.Next()
			return
		}

		c.Next()

	}
}

// func OrderCreateControlForCategory() gin.HandlerFunc {
// 	return func(c *gin.Context) {
// 		account_id, exists := c.Get(state.CurrentUserID)
// 		if !exists {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
// 			return
// 		}

// 		db := database.DBClient()

// 		var (
// 			current_license entities.License
// 		)
// 		if err := db.Model(&entities.License{}).
// 			Where("account_id = ?", account_id).
// 			Order("created_at desc").
// 			First(&current_license).Error; err != nil {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_license_not_found", c.GetString(state.CurrentPhoneLanguage), nil)})
// 			return
// 		}

// 		if current_license.Type == 1 {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_limit_order_count", c.GetString(state.CurrentPhoneLanguage), nil)})
// 			return
// 		}

// 		c.Next()

// 	}
// }
