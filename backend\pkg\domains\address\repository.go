package address

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	addressCreate(ctx context.Context, req dtos.RequestForCreateAddress) error
	addressUpdate(ctx context.Context, req dtos.RequestForUpdateAddress) error
	addressDelete(ctx context.Context, id uuid.UUID) error
	addressGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	addressGetByID(ctx context.Context, id uuid.UUID) (*entities.AddressResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) addressCreate(ctx context.Context, req dtos.RequestForCreateAddress) error {
	var (
		control       int64
		title_control entities.Address
	)

	r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("user_id = ?", state.GetCurrentID(ctx)).
		Where("title = ?", req.Title).
		First(&title_control)

	if title_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistComment)
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("user_id = ?", state.GetCurrentID(ctx)).
		Count(&control).Error; err != nil {
		return err
	}

	if control >= 3 {
		return errors.New("max_address_reached")
	}

	// If this is set as main address, update all other addresses to not be main
	if req.IsMain {
		err := r.db.WithContext(ctx).
			Model(&entities.Address{}).
			Where("user_id = ?", state.GetCurrentID(ctx)).
			Update("is_main", false).Error
		if err != nil {
			return err
		}
	}

	address := &entities.Address{
		AccountID: state.GetCurrentID(ctx),
		Title:     req.Title,
		Line1:     req.Line1,
		Line2:     req.Line2,
		Line3:     req.Line3,
		City:      req.City,
		Zip:       req.Zip,
		Country:   req.Country,
		Lat:       req.Lat,
		Lng:       req.Lng,
		IsMain:    req.IsMain,
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Create(address).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) addressUpdate(ctx context.Context, req dtos.RequestForUpdateAddress) error {
	var address entities.Address
	r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("id = ?", req.ID.String()).
		First(&address)

	if address.ID == uuid.Nil {
		return errors.New(consts.NotFoundAddress)
	}

	// If this is set as main address, update all other addresses to not be main
	if req.IsMain {
		err := r.db.WithContext(ctx).
			Model(&entities.Address{}).
			Where("user_id = ? AND id != ?", state.GetCurrentID(ctx), req.ID.String()).
			Update("is_main", false).Error
		if err != nil {
			return err
		}
	}

	// Update the address
	if err := r.db.WithContext(ctx).
		Model(&address).
		Updates(entities.Address{
			Title:   req.Title,
			Line1:   req.Line1,
			Line2:   req.Line2,
			Line3:   req.Line3,
			City:    req.City,
			Zip:     req.Zip,
			Country: req.Country,
			Lat:     req.Lat,
			Lng:     req.Lng,
			IsMain:  req.IsMain,
		}).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) addressDelete(ctx context.Context, id uuid.UUID) error {
	var address entities.Address
	r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("id = ?", id.String()).
		First(&address)

	if address.ID == uuid.Nil {
		return errors.New(consts.NotFoundAddress)
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("id = ? AND user_id = ?", id.String(), state.GetCurrentID(ctx)).
		Delete(&entities.Address{}).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) addressGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		addresses []entities.Address
		resp      []entities.AddressResponse
		count     int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Address{})

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&addresses).Error; err != nil {
		return nil, err
	}

	for _, v := range addresses {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) addressGetByID(ctx context.Context, id uuid.UUID) (*entities.AddressResponse, error) {
	var (
		address entities.Address
		resp    entities.AddressResponse
	)
	err := r.db.WithContext(ctx).
		Model(&entities.Address{}).
		Where("id = ?", id.String()).
		First(&address).Error

	resp = address.Response()

	return &resp, err
}
