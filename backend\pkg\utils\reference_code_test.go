package utils

import (
	"regexp"
	"strconv"
	"testing"
)

func TestGenerateReferenceCode(t *testing.T) {
	tests := []struct {
		name string
	}{
		{"Generate reference code"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code, err := GenerateReferenceCode()
			
			// Check if no error occurred
			if err != nil {
				t.<PERSON><PERSON>("GenerateReferenceCode() error = %v, want nil", err)
				return
			}
			
			// Check if code length is 8
			if len(code) != 8 {
				t.<PERSON><PERSON><PERSON>("GenerateReferenceCode() length = %d, want 8", len(code))
			}
			
			// Check if code contains only valid characters
			if !IsValidReferenceCode(code) {
				t.<PERSON>("GenerateReferenceCode() = %v, contains invalid characters", code)
			}
			
			// Check if code contains only uppercase letters and numbers
			matched, _ := regexp.MatchString("^[23456789ABCDEFGHJKMNPQRSTUVWXYZ]{8}$", code)
			if !matched {
				t.<PERSON><PERSON><PERSON>("GenerateReferenceCode() = %v, does not match expected pattern", code)
			}
		})
	}
}

func TestGenerateNumericReferenceCode(t *testing.T) {
	tests := []struct {
		name string
	}{
		{"Generate numeric reference code"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code, err := GenerateNumericReferenceCode()
			
			// Check if no error occurred
			if err != nil {
				t.Errorf("GenerateNumericReferenceCode() error = %v, want nil", err)
				return
			}
			
			// Check if code length is 8
			if len(code) != 8 {
				t.Errorf("GenerateNumericReferenceCode() length = %d, want 8", len(code))
			}
			
			// Check if code is numeric
			if !IsValidNumericReferenceCode(code) {
				t.Errorf("GenerateNumericReferenceCode() = %v, contains non-numeric characters", code)
			}
			
			// Check if code is within valid range (10000000-99999999)
			num, err := strconv.ParseInt(code, 10, 64)
			if err != nil {
				t.Errorf("GenerateNumericReferenceCode() = %v, cannot parse as integer", code)
			}
			
			if num < 10000000 || num > 99999999 {
				t.Errorf("GenerateNumericReferenceCode() = %v, not in valid range 10000000-99999999", code)
			}
		})
	}
}

func TestIsValidReferenceCode(t *testing.T) {
	tests := []struct {
		name string
		code string
		want bool
	}{
		{"Valid code", "ABC23456", true},
		{"Valid code with all numbers", "23456789", true},
		{"Valid code with all letters", "ABCDEFGH", true},
		{"Invalid - too short", "ABC234", false},
		{"Invalid - too long", "ABC234567", false},
		{"Invalid - contains 0", "ABC23450", false},
		{"Invalid - contains O", "ABC234O6", false},
		{"Invalid - contains 1", "ABC23416", false},
		{"Invalid - contains I", "ABC234I6", false},
		{"Invalid - contains L", "ABC234L6", false},
		{"Invalid - lowercase", "abc23456", false},
		{"Invalid - special characters", "ABC234@6", false},
		{"Empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsValidReferenceCode(tt.code); got != tt.want {
				t.Errorf("IsValidReferenceCode(%v) = %v, want %v", tt.code, got, tt.want)
			}
		})
	}
}

func TestIsValidNumericReferenceCode(t *testing.T) {
	tests := []struct {
		name string
		code string
		want bool
	}{
		{"Valid numeric code", "12345678", true},
		{"Valid numeric code with zeros", "10000000", true},
		{"Valid numeric code max", "99999999", true},
		{"Invalid - too short", "1234567", false},
		{"Invalid - too long", "123456789", false},
		{"Invalid - contains letters", "1234567A", false},
		{"Invalid - special characters", "1234567@", false},
		{"Empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsValidNumericReferenceCode(tt.code); got != tt.want {
				t.Errorf("IsValidNumericReferenceCode(%v) = %v, want %v", tt.code, got, tt.want)
			}
		})
	}
}

// Benchmark tests
func BenchmarkGenerateReferenceCode(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := GenerateReferenceCode()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkGenerateNumericReferenceCode(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := GenerateNumericReferenceCode()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Test uniqueness by generating multiple codes
func TestReferenceCodeUniqueness(t *testing.T) {
	const numCodes = 1000
	codes := make(map[string]bool)
	
	for i := 0; i < numCodes; i++ {
		code, err := GenerateReferenceCode()
		if err != nil {
			t.Fatalf("GenerateReferenceCode() error = %v", err)
		}
		
		if codes[code] {
			t.Errorf("Duplicate code generated: %v", code)
		}
		codes[code] = true
	}
	
	if len(codes) != numCodes {
		t.Errorf("Expected %d unique codes, got %d", numCodes, len(codes))
	}
}

func TestNumericReferenceCodeUniqueness(t *testing.T) {
	const numCodes = 1000
	codes := make(map[string]bool)
	
	for i := 0; i < numCodes; i++ {
		code, err := GenerateNumericReferenceCode()
		if err != nil {
			t.Fatalf("GenerateNumericReferenceCode() error = %v", err)
		}
		
		if codes[code] {
			t.Errorf("Duplicate code generated: %v", code)
		}
		codes[code] = true
	}
	
	if len(codes) != numCodes {
		t.Errorf("Expected %d unique codes, got %d", numCodes, len(codes))
	}
}
