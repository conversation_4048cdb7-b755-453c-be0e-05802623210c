package localizer

import (
	"encoding/json"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/temizlik-delisi/pkg/embed"
	"golang.org/x/text/language"
)

func GetTranslated(keyword string, langTag string, templateDate map[string]interface{}) string {
	bundle := i18n.NewBundle(language.English)

	bundle.RegisterUnmarshalFunc("json", i18n.UnmarshalFunc(json.Unmarshal))
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/en.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/tr.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/pl.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/es.json")

	localizer := i18n.NewLocalizer(bundle, langTag)

	localizedMessage, _ := localizer.Localize(&i18n.LocalizeConfig{
		MessageID:    keyword,
		TemplateData: templateDate,
	})

	return localizedMessage
}
