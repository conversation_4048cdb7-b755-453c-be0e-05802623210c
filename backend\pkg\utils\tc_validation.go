package utils

import (
	"regexp"
	"strconv"
)

// TCKimlikNoRegex TC kimlik numarası format kontrolü için regex pattern
var TCKimlikNoRegex = regexp.MustCompile(`^[1-9][0-9]{10}$`)

// ValidateTCKimlikNo TC kimlik numarasının hem format hem de algoritma kontrolünü yapar
func ValidateTCKimlikNo(tcKimlikNo string) bool {
	// Format kontrolü: 11 haneli, ilk hane 0 olamaz
	if !TCKimlikNoRegex.MatchString(tcKimlikNo) {
		return false
	}

	// Algoritma kontrolü
	return validateTCAlgorithm(tcKimlikNo)
}

// IsValidTCFormat sadece format kontrolü yapar (regex)
func IsValidTCFormat(tcKimlikNo string) bool {
	return TCKimlikNoRegex.MatchString(tcKimlikNo)
}

// validateTCAlgorithm TC kimlik numarası algoritma kontrolünü yapar
func validateTCAlgorithm(tcKimlikNo string) bool {
	// String'i digit array'e çevir
	digits := make([]int, 11)
	for i, char := range tcKimlikNo {
		digit, err := strconv.Atoi(string(char))
		if err != nil {
			return false
		}
		digits[i] = digit
	}

	// İlk 10 hanenin toplamı
	sum := 0
	for i := 0; i < 10; i++ {
		sum += digits[i]
	}

	// 11. hane kontrolü: İlk 10 hanenin toplamının 10'a bölümünden kalan
	if sum%10 != digits[10] {
		return false
	}

	// 10. hane kontrolü
	oddSum := 0  // 1, 3, 5, 7, 9. hanelerin toplamı
	evenSum := 0 // 2, 4, 6, 8. hanelerin toplamı

	for i := 0; i < 9; i++ {
		if i%2 == 0 {
			oddSum += digits[i]
		} else {
			evenSum += digits[i]
		}
	}

	// 10. hane = ((1, 3, 5, 7, 9. hanelerin toplamı * 7) - (2, 4, 6, 8. hanelerin toplamı)) mod 10
	expectedTenthDigit := ((oddSum * 7) - evenSum) % 10
	if expectedTenthDigit < 0 {
		expectedTenthDigit += 10
	}

	return expectedTenthDigit == digits[9]
}

// NormalizeTCKimlikNo TC kimlik numarasını normalize eder (boşlukları ve özel karakterleri temizler)
func NormalizeTCKimlikNo(tcKimlikNo string) string {
	// Sadece rakamları al
	re := regexp.MustCompile(`[^0-9]`)
	return re.ReplaceAllString(tcKimlikNo, "")
}
