package cleaner

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/cleaner"
	"github.com/temizlik-delisi/pkg/domains/verification"
	"github.com/temizlik-delisi/pkg/middleware"
)

func CleanerRoutes(r *gin.RouterGroup, s cleaner.Service, vs verification.Service) {
	c := r.Group("/cleaner")

	c.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(2))
	{
		c.GET("/language", GetLanguages(s))
		c.POST("/language", CreateLanguage(s))
		c.DELETE("/language/:id", DeleteLanguage(s))

		c.GET("/work-experience", GetWorkExperiences(s))
		c.GET("/work-experience/:id", GetWorkExperience(s))
		c.POST("/work-experience", CreateWorkExperience(s))
		c.PUT("/work-experience/:id", UpdateWorkExperience(s))
		c.DELETE("/work-experience/:id", DeleteWorkExperience(s))

		c.POST("/tc-no", VerifyTC(vs))
		c.POST("/criminal-record", UploadCriminalRecordDocument(vs))
		c.DELETE("/criminal-record", DeleteCriminalRecordDocument(vs))
	}
}
