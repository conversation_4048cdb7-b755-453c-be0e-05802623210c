package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strconv"
	"strings"
)

// GenerateReferenceCode generates an 8-digit reference code
// The code consists of uppercase letters and numbers for better readability
// Excludes confusing characters like 0, O, 1, I, L to avoid confusion
func GenerateReferenceCode() (string, error) {
	// Character set excluding confusing characters
	charset := "23456789ABCDEFGHJKMNPQRSTUVWXYZ"
	
	var result strings.Builder
	result.Grow(8) // Pre-allocate capacity for 8 characters
	
	for i := 0; i < 8; i++ {
		// Generate random index
		randomIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", fmt.Errorf("failed to generate random number: %w", err)
		}
		
		result.WriteByte(charset[randomIndex.Int64()])
	}
	
	return result.String(), nil
}

// GenerateNumericReferenceCode generates an 8-digit numeric reference code
// Returns a string representation of the 8-digit number with leading zeros if necessary
func GenerateNumericReferenceCode() (string, error) {
	// Generate random number between 10000000 and 99999999 (8 digits)
	min := int64(10000000)
	max := int64(99999999)
	
	randomNum, err := rand.Int(rand.Reader, big.NewInt(max-min+1))
	if err != nil {
		return "", fmt.Errorf("failed to generate random number: %w", err)
	}
	
	// Add min to ensure we get a number in the desired range
	result := randomNum.Int64() + min
	
	return strconv.FormatInt(result, 10), nil
}

// IsValidReferenceCode validates if a reference code is in the correct format
func IsValidReferenceCode(code string) bool {
	if len(code) != 8 {
		return false
	}
	
	// Check if all characters are alphanumeric and uppercase
	for _, char := range code {
		if !((char >= '2' && char <= '9') || (char >= 'A' && char <= 'Z')) {
			return false
		}
		// Exclude confusing characters
		if char == '0' || char == 'O' || char == '1' || char == 'I' || char == 'L' {
			return false
		}
	}
	
	return true
}

// IsValidNumericReferenceCode validates if a numeric reference code is in the correct format
func IsValidNumericReferenceCode(code string) bool {
	if len(code) != 8 {
		return false
	}
	
	// Check if all characters are digits
	for _, char := range code {
		if char < '0' || char > '9' {
			return false
		}
	}
	
	return true
}
