package address

import (
	"context"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
)

type Service interface {
	AddressCreate(ctx context.Context, req dtos.RequestForCreateAddress) error
	AddressUpdate(ctx context.Context, req dtos.RequestForUpdateAddress) error
	AddressDelete(ctx context.Context, id uuid.UUID) error
	AddressGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	AddressGetByID(ctx context.Context, id uuid.UUID) (*entities.AddressResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) AddressCreate(ctx context.Context, req dtos.RequestForCreateAddress) error {
	return s.repository.addressCreate(ctx, req)
}

func (s *service) AddressUpdate(ctx context.Context, req dtos.RequestForUpdateAddress) error {
	return s.repository.addressUpdate(ctx, req)
}

func (s *service) AddressDelete(ctx context.Context, id uuid.UUID) error {
	return s.repository.addressDelete(ctx, id)
}

func (s *service) AddressGetAll(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	return s.repository.addressGetAll(ctx, page, per_page)
}

func (s *service) AddressGetByID(ctx context.Context, id uuid.UUID) (*entities.AddressResponse, error) {
	return s.repository.addressGetByID(ctx, id)
}
