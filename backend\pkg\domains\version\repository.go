package version

import (
	"context"

	"github.com/temizlik-delisi/pkg/entities"
	"gorm.io/gorm"
)

type Respository interface {
	getVersion(ctx context.Context) (entities.Version, error)
}
type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Respository {
	return &repository{
		db: db,
	}
}

func (r *repository) getVersion(ctx context.Context) (entities.Version, error) {
	var current_version entities.Version
	err := r.db.WithContext(ctx).
		Model(&entities.Version{}).
		Order("created_at desc").
		First(&current_version).Error
	return current_version, err
}
