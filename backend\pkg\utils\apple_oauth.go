package utils

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/dtos"
)

type AppleOAuth struct {
	ClientID     string
	TeamID       string
	KeyID        string
	PrivateKey   *rsa.PrivateKey
	RedirectURL  string
}

type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}

func NewAppleOAuth() *AppleOAuth {
	cfg := config.InitConfig()

	// Parse private key from config
	privateKey, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(cfg.Apple.PrivateKey))
	if err != nil {
		panic(fmt.Sprintf("Failed to parse Apple private key: %v", err))
	}

	return &AppleOAuth{
		ClientID:    cfg.Apple.ClientID,
		TeamID:      cfg.Apple.TeamID,
		KeyID:       cfg.Apple.KeyID,
		PrivateKey:  privateKey,
		RedirectURL: cfg.Apple.RedirectURL,
	}
}

// ExchangeCodeForToken exchanges authorization code for access token
func (a *AppleOAuth) ExchangeCodeForToken(code string) (*AppleTokenResponse, error) {
	// Create client secret JWT
	clientSecret, err := a.generateClientSecret()
	if err != nil {
		return nil, fmt.Errorf("failed to generate client secret: %v", err)
	}

	// Prepare form data
	data := url.Values{}
	data.Set("client_id", a.ClientID)
	data.Set("client_secret", clientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", a.RedirectURL)

	// Make request to Apple's token endpoint
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("POST", "https://appleid.apple.com/auth/token", strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("apple token request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("apple token endpoint returned status: %d, body: %s", resp.StatusCode, string(body))
	}

	var tokenResponse AppleTokenResponse
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, fmt.Errorf("failed to parse token response: %v", err)
	}

	return &tokenResponse, nil
}

// VerifyAppleIDToken verifies Apple ID token and extracts user info
func (a *AppleOAuth) VerifyAppleIDToken(idToken string) (*dtos.AppleUserInfo, error) {
	// Parse the JWT token without verification first to get the header
	token, _, err := new(jwt.Parser).ParseUnverified(idToken, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse ID token: %v", err)
	}

	// Get the key ID from the header
	keyID, ok := token.Header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("no key ID found in token header")
	}

	// Get Apple's public keys
	publicKey, err := a.getApplePublicKey(keyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get Apple public key: %v", err)
	}

	// Verify the token with the public key
	parsedToken, err := jwt.Parse(idToken, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to verify ID token: %v", err)
	}

	if !parsedToken.Valid {
		return nil, fmt.Errorf("invalid ID token")
	}

	// Extract claims
	claims, ok := parsedToken.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to parse token claims")
	}

	// Verify audience (client_id)
	if aud, ok := claims["aud"].(string); !ok || aud != a.ClientID {
		return nil, fmt.Errorf("invalid audience in token")
	}

	// Verify issuer
	if iss, ok := claims["iss"].(string); !ok || iss != "https://appleid.apple.com" {
		return nil, fmt.Errorf("invalid issuer in token")
	}

	// Extract user info
	userInfo := &dtos.AppleUserInfo{
		ID:            getStringFromClaims(claims, "sub"),
		Email:         getStringFromClaims(claims, "email"),
		VerifiedEmail: getBoolFromClaims(claims, "email_verified"),
	}

	// Apple doesn't always provide name in the ID token
	// Name is usually provided only on first sign-in
	if name, exists := claims["name"].(map[string]interface{}); exists {
		userInfo.GivenName = getStringFromMap(name, "firstName")
		userInfo.FamilyName = getStringFromMap(name, "lastName")
		userInfo.Name = userInfo.GivenName + " " + userInfo.FamilyName
	}

	return userInfo, nil
}

// generateClientSecret creates a JWT client secret for Apple
func (a *AppleOAuth) generateClientSecret() (string, error) {
	now := time.Now()
	
	claims := jwt.MapClaims{
		"iss": a.TeamID,
		"iat": now.Unix(),
		"exp": now.Add(time.Hour * 24 * 180).Unix(), // 6 months max
		"aud": "https://appleid.apple.com",
		"sub": a.ClientID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = a.KeyID

	return token.SignedString(a.PrivateKey)
}

// getApplePublicKey fetches Apple's public key for token verification
func (a *AppleOAuth) getApplePublicKey(keyID string) (*rsa.PublicKey, error) {
	// This is a simplified version. In production, you should cache the keys
	// and implement proper key rotation handling
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get("https://appleid.apple.com/auth/keys")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple public keys: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read keys response: %v", err)
	}

	var keysResponse struct {
		Keys []map[string]interface{} `json:"keys"`
	}

	if err := json.Unmarshal(body, &keysResponse); err != nil {
		return nil, fmt.Errorf("failed to parse keys response: %v", err)
	}

	// Find the key with matching kid
	for _, key := range keysResponse.Keys {
		if kid, ok := key["kid"].(string); ok && kid == keyID {
			// Convert JWK to RSA public key
			// This is a simplified implementation
			// In production, use a proper JWK library
			return nil, fmt.Errorf("JWK to RSA conversion not implemented - use a JWK library")
		}
	}

	return nil, fmt.Errorf("public key not found for key ID: %s", keyID)
}

// Helper functions
func getStringFromClaims(claims jwt.MapClaims, key string) string {
	if val, ok := claims[key].(string); ok {
		return val
	}
	return ""
}

func getBoolFromClaims(claims jwt.MapClaims, key string) bool {
	if val, ok := claims[key].(bool); ok {
		return val
	}
	return false
}
