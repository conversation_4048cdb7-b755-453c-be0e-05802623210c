package dtos

import "github.com/google/uuid"

type RequestForCreateAddress struct {
	Title    string  `json:"title" example:"home"`
	Line1    string  `json:"line1" example:"Cumhuriyet Mah."`
	Line2    string  `json:"line2" example:"Cumhuriyet Cd."`
	Line3    string  `json:"line3" example:"Cumhuriyet Sk."`
	City     string  `json:"city" validate:"required" example:"Istanbul"`
	District string  `json:"district" example:"Bakirkoy"`
	Zip      string  `json:"zip" example:"34000"`
	Country  string  `json:"country" validate:"required" example:"Turkey"`
	Lat      float64 `json:"lat" example:"41.0082"`
	Lng      float64 `json:"lng" example:"28.9784"`
	IsMain   bool    `json:"is_main" example:"false"`
}

type RequestForUpdateAddress struct {
	ID       uuid.UUID `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title    string    `json:"title" example:"home"`
	Line1    string    `json:"line1" example:"Cumhuriyet Mah."`
	Line2    string    `json:"line2" example:"Cumhuriyet Cd."`
	Line3    string    `json:"line3" example:"Cumhuriyet Sk."`
	City     string    `json:"city" validate:"required" example:"Istanbul"`
	District string    `json:"district" example:"Bakirkoy"`
	Zip      string    `json:"zip" example:"34000"`
	Country  string    `json:"country" validate:"required" example:"Turkey"`
	Lat      float64   `json:"lat" example:"41.0082"`
	Lng      float64   `json:"lng" example:"28.9784"`
	IsMain   bool      `json:"is_main" example:"false"`
}

type RequestForDeleteAddress struct {
	ID string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
