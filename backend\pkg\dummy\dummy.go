package dummy

import (
	"context"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/database"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/utils"
	"gorm.io/gorm"
)

// TODO: diğer tablolarda veri oluşturacak daha fazla dummy ekle
func CreateDummy() {
	db := database.DBClient()

	customer := CreateDummyCustomer(db)
	CreateCustomerAddress(db, customer)
	CreateDummyCustomerFreeLicense(db, customer)

	cleaner := CreateDummyCleaner(db)
	CreateCleanerAddress(db, cleaner)
	CreateDummyCleanerFreeLicense(db, cleaner)

	CreateDummyServiceCategory(db)

}

func CreateDummyCustomer(db *gorm.DB) *entities.Customer {
	var (
		customer entities.Customer
	)

	db.Model(&entities.Customer{}).
		Where("email = ?", "<EMAIL>").
		First(&customer)

	if customer.ID != uuid.Nil {
		return &customer
	}

	pass := utils.Bcrypt("123456")
	customer = entities.Customer{
		BaseInfo: entities.BaseInfo{
			Name:                   "Joe",
			Surname:                "MacMillan",
			Email:                  "<EMAIL>",
			Password:               &pass,
			Provider:               "local",
			TimeZone:               "Europe/Istanbul",
			PhoneLanguage:          "TR",
			Os:                     "android",
			LastVersionBuildNumber: 1,
			PurchaseID:             "1234567890a",
			PushNotifToken:         "1234567890a",
			LastVersionName:        "1.0.0",
			LastLoginDate:          time.Now(),
			ReferenceID:            "ABC45678a",
		},
	}

	db.Create(&customer)

	cfg := config.InitConfig()
	jwtWrapper := utils.JwtWrapper{
		SecretKey: cfg.App.JwtSecret,
		Issuer:    cfg.App.JwtIssuer,
		Expire:    cfg.App.JwtExpire,
	}

	ctx := context.Background()
	token, err := jwtWrapper.GenerateJWT(ctx, customer.ID.String(), customer.Email, customer.TimeZone, customer.PhoneLanguage, customer.PushNotifToken, customer.PurchaseID, customer.Os, 1)
	if err != nil {
		log.Println("**** customer_token_error: ", err)
	}
	log.Println("**** customer_token: ", token)

	return &customer
}

func CreateDummyCleaner(db *gorm.DB) *entities.Cleaner {

	var cleaner entities.Cleaner

	db.Model(&entities.Cleaner{}).
		Where("email = ?", "<EMAIL>").
		First(&cleaner)

	if cleaner.ID != uuid.Nil {
		return &cleaner
	}

	pass := utils.Bcrypt("123456")

	cleaner = entities.Cleaner{
		BaseInfo: entities.BaseInfo{
			Name:                   "Gordon",
			Surname:                "Clark",
			Email:                  "<EMAIL>",
			Password:               &pass,
			Provider:               "local",
			TimeZone:               "Europe/Istanbul",
			PhoneLanguage:          "TR",
			Os:                     "android",
			LastVersionBuildNumber: 1,
			PurchaseID:             "1234567890b",
			PushNotifToken:         "1234567890b",
			LastVersionName:        "1.0.0",
			LastLoginDate:          time.Now(),
			ReferenceID:            "ABC45678b",
		},
		TCNo:                           "12345678901",
		TCNoVerified:                   true,
		CriminalRecordDocumentURL:      "https://minio.example.com/criminal-records/document.pdf",
		CriminalRecordDocumentVerified: true,
	}

	db.Create(&cleaner)

	cfg := config.InitConfig()
	jwtWrapper := utils.JwtWrapper{
		SecretKey: cfg.App.JwtSecret,
		Issuer:    cfg.App.JwtIssuer,
		Expire:    cfg.App.JwtExpire,
	}

	ctx := context.Background()
	token, err := jwtWrapper.GenerateJWT(ctx, cleaner.ID.String(), cleaner.Email, cleaner.TimeZone, cleaner.PhoneLanguage, cleaner.PushNotifToken, cleaner.PurchaseID, cleaner.Os, 2)
	if err != nil {
		log.Println("**** cleaner_token_error: ", err)
	}
	log.Println("**** cleaner_token: ", token)

	return &cleaner
}

func CreateCustomerAddress(db *gorm.DB, customer *entities.Customer) {

	var address entities.Address
	db.Model(&entities.Address{}).
		Where("account_id = ?", customer.ID).
		Where("title = ?", "joe_macmillan_home").
		First(&address)

	if address.ID != uuid.Nil {
		return
	}

	address = entities.Address{
		Base: entities.Base{
			ID: uuid.New(),
		},
		AccountID:   customer.ID,
		AccountType: 1,
		Title:       "joe_macmillan_home",
		Line1:       "Cumhuriyet Mah.",
		Line2:       "Cumhuriyet Cd.",
		Line3:       "Cumhuriyet Sk.",
		City:        "Istanbul",
		Zip:         "34000",
		Country:     "Turkey",
		Lat:         37.030616,
		Lng:         35.416533,
		IsMain:      true,
	}

	db.Model(&entities.Address{}).
		Create(&address)
}

func CreateCleanerAddress(db *gorm.DB, cleaner *entities.Cleaner) {
	var address entities.Address
	db.Model(&entities.Address{}).
		Where("account_id = ?", cleaner.ID).
		Where("title = ?", "gordon_clark_home").
		First(&address)

	if address.ID != uuid.Nil {
		return
	}

	address = entities.Address{
		Base: entities.Base{
			ID: uuid.New(),
		},
		AccountID:   cleaner.ID,
		AccountType: 2,
		Title:       "gordon_clark_home",
		Line1:       "Cumhuriyet Mah.",
		Line2:       "Cumhuriyet Cd.",
		Line3:       "Cumhuriyet Sk.",
		City:        "Istanbul",
		Zip:         "34000",
		Country:     "Turkey",
		Lat:         37.037987,
		Lng:         35.397398,
		IsMain:      true,
	}

	db.Model(&entities.Address{}).
		Create(&address)
}

func CreateDummyCustomerFreeLicense(db *gorm.DB, customer *entities.Customer) {
	var license entities.License
	db.Model(&entities.License{}).
		Where("account_id = ?", customer.ID).
		First(&license)

	if license.ID != uuid.Nil {
		return
	}

	license = entities.License{
		Type:               1,
		OrderCount:         1,
		AdRate:             1,
		DoSeeEveryCategory: false,
		AccountID:          customer.ID,
		AccountType:        1,
	}

	db.Model(&entities.License{}).
		Create(&license)
}

func CreateDummyCleanerFreeLicense(db *gorm.DB, cleaner *entities.Cleaner) {
	var license entities.License
	db.Model(&entities.License{}).
		Where("account_id = ?", cleaner.ID).
		First(&license)

	if license.ID != uuid.Nil {
		return
	}

	license = entities.License{
		Type:               1,
		OrderCount:         2,
		AdRate:             1,
		DoSeeEveryCategory: false,
		AccountID:          cleaner.ID,
		AccountType:        2,
	}

	db.Model(&entities.License{}).
		Create(&license)
}

func CreateDummyServiceCategory(db *gorm.DB) {
	// Önce main kategoriler
	mainCategories := []entities.ServiceCategory{
		{
			ServiceNameTR:         "Temizlik",
			ServiceNameEN:         "Cleaning",
			Description:           "It's main category for cleaning",
			MinDuration:           0,
			ApprovedByAdmin:       true,
			IsMain:                true,
			MainCategoryID:        uuid.Nil, // Main kategorinin kendisi, üstü yok
			PermittedLisanceTypes: []int64{1, 2, 3},
		},
		{
			ServiceNameTR:         "Yemek Pişirme",
			ServiceNameEN:         "Cooking",
			Description:           "It's main category for cooking",
			MinDuration:           0,
			ApprovedByAdmin:       true,
			IsMain:                true,
			MainCategoryID:        uuid.Nil,
			PermittedLisanceTypes: []int64{3},
		},
	}

	// DB'ye yaz, ID’leri al
	for i := range mainCategories {
		var existing entities.ServiceCategory
		err := db.Where("service_name_tr = ?", mainCategories[i].ServiceNameTR).First(&existing).Error
		if err == nil {
			// zaten var
			mainCategories[i].ID = existing.ID
			continue
		}

		if err := db.Create(&mainCategories[i]).Error; err != nil {
			panic(err)
		}
	}

	// Şimdi alt kategoriler, üst kategorilerin ID'siyle
	subCategories := []entities.ServiceCategory{
		{
			ServiceNameTR:         "Standart Temizlik",
			ServiceNameEN:         "Standart Cleaning",
			Description:           "süpürme, silme, mutfak, banyo temizliği gibi temel temizlik. ",
			MinDuration:           60,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[0].ID, // "Temizlik" ID’si
			PermittedLisanceTypes: []int64{1, 2, 3},
		},
		{
			ServiceNameTR:         "Derin Temizlik",
			ServiceNameEN:         "Deep Cleaning",
			Description:           "standart'a ek olarak, halı, koltuk, fırın, buzdolabı gibi detaylı işleri de içerir.",
			MinDuration:           120,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[0].ID, // "Temizlik" ID’si
			PermittedLisanceTypes: []int64{2, 3},
		},
		{
			ServiceNameTR:         "Özel Temizlik",
			ServiceNameEN:         "Special Cleaning",
			Description:           "taşınma sonrası, inşaat sonrası veya parti sonrası için temizlik.",
			MinDuration:           240,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[0].ID, // "Temizlik" ID’si
			PermittedLisanceTypes: []int64{2, 3},
		},
		{
			ServiceNameTR:         "Ofis Temizlik",
			ServiceNameEN:         "Office Cleaning",
			Description:           "Ofis temizliği.",
			MinDuration:           240,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[0].ID, // "Temizlik" ID’si
			PermittedLisanceTypes: []int64{2, 3},
		},
		{
			ServiceNameTR:         "Acil Temizlik",
			ServiceNameEN:         "Emergency Cleaning",
			Description:           "Acil temizlik.",
			MinDuration:           120,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[0].ID, // "Temizlik" ID’si
			PermittedLisanceTypes: []int64{2, 3},
		},
		{
			ServiceNameTR:         "Evde Yemek",
			ServiceNameEN:         "Home Cooking",
			Description:           "Cooking at home sub category",
			MinDuration:           90,
			ApprovedByAdmin:       true,
			IsMain:                false,
			MainCategoryID:        mainCategories[1].ID, // "Yemek Pişirme" ID’si
			PermittedLisanceTypes: []int64{3},
		},
	}

	for i := range subCategories {
		var existing entities.ServiceCategory
		err := db.Where("service_name_tr = ?", subCategories[i].ServiceNameTR).First(&existing).Error
		if err == nil {
			continue
		}

		if err := db.Create(&subCategories[i]).Error; err != nil {
			panic(err)
		}
	}
}
