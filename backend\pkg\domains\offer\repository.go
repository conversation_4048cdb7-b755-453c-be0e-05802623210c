package offer

import (
	"context"
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	getOffersByOrderID(ctx context.Context, page, per_page int, order_id uuid.UUID) (*dtos.PaginatedData, error)
	getOfferByID(ctx context.Context, id uuid.UUID) (*entities.OfferResponse, error)
	getMyOffers(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error)
	createOffer(ctx context.Context, req dtos.RequestForCreateOffer) error
	deleteOffer(ctx context.Context, id uuid.UUID) error
	updateOffer(ctx context.Context, req dtos.RequestForUpdateOffer) error
	acceptOffer(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getOffersByOrderID(ctx context.Context, page, per_page int, order_id uuid.UUID) (*dtos.PaginatedData, error) {
	var (
		offers []entities.Offer
		resp   []entities.OfferResponse
		count  int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("order_id = ?", order_id)

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&offers).Error; err != nil {
		return nil, err
	}

	for _, v := range offers {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) getOfferByID(ctx context.Context, id uuid.UUID) (*entities.OfferResponse, error) {
	var offer entities.Offer
	err := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", id).
		First(&offer).Error

	resp := offer.Response()

	return &resp, err
}

func (r *repository) getMyOffers(ctx context.Context, page, per_page int) (*dtos.PaginatedData, error) {
	var (
		offers []entities.Offer
		resp   []entities.OfferResponse
		count  int64
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("bidder_id = ?", state.GetCurrentID(ctx)).
		Where("bidder_account_type = ?", state.GetCurrentAccountType(ctx))

	if err := base_query.
		Limit(per_page).
		Offset((page - 1) * per_page).
		Order("created_at desc").
		Find(&offers).Error; err != nil {
		return nil, err
	}
	for _, v := range offers {
		resp = append(resp, v.Response())
	}

	if err := base_query.Count(&count).Error; err != nil {
		return nil, err
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(per_page),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(per_page))),
		IsLastPage: page >= int(math.Ceil(float64(count)/float64(per_page))),
		Rows:       resp,
	}, nil
}

func (r *repository) createOffer(ctx context.Context, req dtos.RequestForCreateOffer) error {
	var offer_control entities.Offer
	r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("order_id = ?", req.OrderID).
		Where("bidder_id = ?", state.GetCurrentID(ctx)).
		First(&offer_control)

	if offer_control.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistOffer)
	}

	var order_control entities.Order
	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", req.OrderID).
		First(&order_control)

	if order_control.ID == uuid.Nil {
		return errors.New(consts.NotFoundOrder)
	}

	if order_control.AccountType == state.GetCurrentAccountType(ctx) {
		return errors.New("you_cannot_bid_on_your_own_order")
	}

	if !(order_control.Status == 5 || order_control.Status == 4 || order_control.Status == 3) {
		return errors.New("you_cannot_bid_on_this_order")
	}

	// status'u listed'den receive offers'a çek
	r.db.WithContext(ctx).
		Model(&entities.Order{}).
		Where("id = ?", req.OrderID).
		Updates(entities.Order{
			Status: 2,
		})

	switch state.GetCurrentAccountType(ctx) {
	case 1:
		var customer_control entities.Customer
		r.db.WithContext(ctx).
			Model(&entities.Customer{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			First(&customer_control)

		if customer_control.ID == uuid.Nil {
			return errors.New(consts.NotFoundCustomer)
		}
	case 2:
		var cleaner_control entities.Cleaner
		r.db.WithContext(ctx).
			Model(&entities.Cleaner{}).
			Where("id = ?", state.GetCurrentID(ctx)).
			First(&cleaner_control)

		if cleaner_control.ID == uuid.Nil {
			return errors.New(consts.NotFoundCleaner)
		}
	default:
		return errors.New("not_found_account_type")
	}

	offer := &entities.Offer{
		OrderID:           uuid.MustParse(req.OrderID),
		BidderID:          state.GetCurrentID(ctx),
		BidderAccountType: state.GetCurrentAccountType(ctx),
		Description:       req.Description,
		NewStartDate:      req.NewStartDate,
		NewEndDate:        req.NewEndDate,
		NewPrice:          req.NewPrice,
	}
	if err := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Create(offer).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) updateOffer(ctx context.Context, req dtos.RequestForUpdateOffer) error {
	var offer entities.Offer
	r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", req.ID).
		First(&offer)

	if offer.ID == uuid.Nil {
		return errors.New(consts.NotFoundOffer)
	}

	offer.Description = req.Description
	offer.NewStartDate = req.NewStartDate
	offer.NewEndDate = req.NewEndDate
	offer.NewPrice = req.NewPrice

	err := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", req.ID).
		Save(&offer).Error

	return err
}

func (r *repository) deleteOffer(ctx context.Context, id uuid.UUID) error {
	var offer entities.Offer
	r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", id).
		First(&offer)

	if offer.ID == uuid.Nil {
		return errors.New(consts.NotFoundOffer)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Delete(&offer).Error

	return err
}

func (r *repository) acceptOffer(ctx context.Context, id uuid.UUID) error {
	var offer entities.Offer
	r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", id).
		First(&offer)

	if offer.ID == uuid.Nil {
		return errors.New(consts.NotFoundOffer)
	}

	offer.IsAccepted = true

	err := r.db.WithContext(ctx).
		Model(&entities.Offer{}).
		Where("id = ?", id).
		Save(&offer).Error

	return err
}
