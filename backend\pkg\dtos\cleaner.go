package dtos

type RequestForCreateLanguage struct {
	Name  string `json:"name" validate:"required" example:"Turkish"`
	Code  string `json:"code" validate:"required" example:"TR"`
	Level string `json:"level" validate:"required" example:"conversational"` // basic, conversational, fluent, native
}

type ResponseForLanguage struct {
	ID        string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt string `json:"created_at" example:"2021-01-01 00:00:00"`
	Name      string `json:"name" example:"Turkish"`
	Code      string `json:"code" example:"TR"`
	Level     string `json:"level" example:"conversational"` // basic, conversational, fluent, native
	CleanerID string `json:"cleaner_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

// GetLanguagesResponse represents the response structure for GetLanguages endpoint
type GetLanguagesResponse struct {
	Data   []ResponseForLanguage `json:"data"`
	Status int                   `json:"status" example:"200"`
}

// Work Experience DTOs
type RequestForCreateWorkExperience struct {
	Title       string `json:"title" validate:"required" example:"Senior Cleaner"`
	Company     string `json:"company" validate:"required" example:"ABC Cleaning Services"`
	Location    string `json:"location" validate:"required" example:"Istanbul"`
	Country     string `json:"country" validate:"required" example:"Turkey"`
	StartDate   string `json:"start_date" validate:"required" example:"2020-01-01"`
	EndDate     string `json:"end_date" example:"2023-12-31"`
	Description string `json:"description" example:"Responsible for residential cleaning services"`
	Current     bool   `json:"current" example:"false"`
}

type RequestForUpdateWorkExperience struct {
	ID          string `json:"id" validate:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title       string `json:"title" validate:"required" example:"Senior Cleaner"`
	Company     string `json:"company" validate:"required" example:"ABC Cleaning Services"`
	Location    string `json:"location" validate:"required" example:"Istanbul"`
	Country     string `json:"country" validate:"required" example:"Turkey"`
	StartDate   string `json:"start_date" validate:"required" example:"2020-01-01"`
	EndDate     string `json:"end_date" example:"2023-12-31"`
	Description string `json:"description" example:"Responsible for residential cleaning services"`
	Current     bool   `json:"current" example:"false"`
}

type ResponseForWorkExperience struct {
	ID          string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt   string `json:"created_at" example:"2021-01-01 00:00:00"`
	Title       string `json:"title" example:"Senior Cleaner"`
	Company     string `json:"company" example:"ABC Cleaning Services"`
	Location    string `json:"location" example:"Istanbul"`
	Country     string `json:"country" example:"Turkey"`
	StartDate   string `json:"start_date" example:"2020-01-01"`
	EndDate     string `json:"end_date" example:"2023-12-31"`
	Description string `json:"description" example:"Responsible for residential cleaning services"`
	Current     bool   `json:"current" example:"false"`
	CleanerID   string `json:"cleaner_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

// GetWorkExperiencesResponse represents the response structure for GetWorkExperiences endpoint
type GetWorkExperiencesResponse struct {
	Data   []ResponseForWorkExperience `json:"data"`
	Status int                         `json:"status" example:"200"`
}

// GetWorkExperienceResponse represents the response structure for GetWorkExperience endpoint
type GetWorkExperienceResponse struct {
	Data   ResponseForWorkExperience `json:"data"`
	Status int                       `json:"status" example:"200"`
}
