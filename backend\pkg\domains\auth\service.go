package auth

import (
	"context"
	"fmt"

	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/utils"
)

type Service interface {
	GoogleLoginRegister(ctx context.Context, req dtos.RequestForGoogleLogin) (*dtos.AuthenticationResponse, error)
	AppleLoginRegister(ctx context.Context, req dtos.RequestForAppleLogin) (*dtos.AuthenticationResponse, error)

	DeleteCustomer(ctx context.Context) error
	DeleteCleaner(ctx context.Context) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

/*
Backend Manuel Test İçin Gereklilikler;

1- google client Authorized redirect URIs ekle (google cloud console'a git)
2- aşağıda verilen url'i tarayıcıya yapıştır;
https://accounts.google.com/o/oauth2/v2/auth?client_id=************-g98ok0pefpc6olsjgd5sui9jl0enj5a7.apps.googleusercontent.com&redirect_uri=http://localhost:3000/google-callback&response_type=code&scope=openid%20email%20profile&access_type=offline&prompt=consent
3. eposta ile devam et ve linkten code'ı al
4. code decode et /google-login isteğine ekle
5. isteği gönder
*/
func (s *service) GoogleLoginRegister(ctx context.Context, req dtos.RequestForGoogleLogin) (*dtos.AuthenticationResponse, error) {
	var id, email, timezone, phone_language, push_notif_token, purchase_id, os string
	googleOAuth := utils.NewGoogleOAuth()
	// exchanged_google_token, err := googleOAuth.ExchangeCodeForToken(req.GoogleCode)
	// if err != nil {
	// 	return nil, fmt.Errorf("google token exchange failed: %v", err)
	// }

	user_info, err := googleOAuth.ValidateGoogleIDToken(req.GoogleIDToken)
	if err != nil {
		return nil, fmt.Errorf("google token verification failed: %v", err)
	}

	if req.AccountType == 1 {
		customer, err := s.repository.GoogleLoginForCustomer(ctx, req, user_info)
		if err != nil {
			return nil, fmt.Errorf("google login failed: %v", err)
		}
		id = customer.ID.String()
		email = customer.Email
		timezone = customer.TimeZone
		phone_language = customer.PhoneLanguage
		push_notif_token = customer.PushNotifToken
		purchase_id = customer.PurchaseID
		os = customer.Os
	}
	if req.AccountType == 2 {
		cleaner, err := s.repository.GoogleLoginForCleaner(ctx, req, user_info)
		if err != nil {
			return nil, fmt.Errorf("google login failed: %v", err)
		}
		id = cleaner.ID.String()
		email = cleaner.Email
		timezone = cleaner.TimeZone
		phone_language = cleaner.PhoneLanguage
		push_notif_token = cleaner.PushNotifToken
		purchase_id = cleaner.PurchaseID
		os = cleaner.Os
	}

	cfg := config.InitConfig()
	jwtWrapper := utils.JwtWrapper{
		SecretKey: cfg.App.JwtSecret,
		Issuer:    cfg.App.JwtIssuer,
		Expire:    cfg.App.JwtExpire,
	}

	token, err := jwtWrapper.GenerateJWT(ctx, id, email, timezone, phone_language, push_notif_token, purchase_id, os, req.AccountType)
	if err != nil {
		return nil, fmt.Errorf("token generation failed: %v", err)
	}

	response := &dtos.AuthenticationResponse{
		Token:       token,
		IsSucceeded: true,
	}

	return response, nil
}

func (s *service) AppleLoginRegister(ctx context.Context, req dtos.RequestForAppleLogin) (*dtos.AuthenticationResponse, error) {
	var id, email, timezone, phone_language, push_notif_token, purchase_id, os string
	appleOAuth := utils.NewAppleOAuth()
	exchanged_apple_token, err := appleOAuth.ExchangeCodeForToken(req.AppleCode)
	if err != nil {
		return nil, fmt.Errorf("apple token exchange failed: %v", err)
	}

	user_info, err := appleOAuth.VerifyAppleIDToken(exchanged_apple_token.IDToken)
	if err != nil {
		return nil, fmt.Errorf("apple token verification failed: %v", err)
	}

	if req.AccountType == 1 {
		customer, err := s.repository.AppleLoginForCustomer(ctx, req, user_info)
		if err != nil {
			return nil, fmt.Errorf("apple login failed: %v", err)
		}
		id = customer.ID.String()
		email = customer.Email
		timezone = customer.TimeZone
		phone_language = customer.PhoneLanguage
		push_notif_token = customer.PushNotifToken
		purchase_id = customer.PurchaseID
		os = customer.Os
	}
	if req.AccountType == 2 {
		cleaner, err := s.repository.AppleLoginForCleaner(ctx, req, user_info)
		if err != nil {
			return nil, fmt.Errorf("apple login failed: %v", err)
		}
		id = cleaner.ID.String()
		email = cleaner.Email
		timezone = cleaner.TimeZone
		phone_language = cleaner.PhoneLanguage
		push_notif_token = cleaner.PushNotifToken
		purchase_id = cleaner.PurchaseID
		os = cleaner.Os
	}

	cfg := config.InitConfig()
	jwtWrapper := utils.JwtWrapper{
		SecretKey: cfg.App.JwtSecret,
		Issuer:    cfg.App.JwtIssuer,
		Expire:    cfg.App.JwtExpire,
	}

	token, err := jwtWrapper.GenerateJWT(ctx, id, email, timezone, phone_language, push_notif_token, purchase_id, os, req.AccountType)
	if err != nil {
		return nil, fmt.Errorf("token generation failed: %v", err)
	}

	response := &dtos.AuthenticationResponse{
		Token:       token,
		IsSucceeded: true,
	}

	return response, nil
}

func (s *service) DeleteCustomer(ctx context.Context) error {
	return s.repository.deleteCustomer(ctx)
}

func (s *service) DeleteCleaner(ctx context.Context) error {
	return s.repository.deleteCleaner(ctx)
}
