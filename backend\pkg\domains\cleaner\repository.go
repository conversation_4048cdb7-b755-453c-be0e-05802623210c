package cleaner

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/consts"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	GetLanguages(ctx context.Context) ([]dtos.ResponseForLanguage, error)
	CreateLanguage(ctx context.Context, req dtos.RequestForCreateLanguage) error
	DeleteLanguage(ctx context.Context, id uuid.UUID) error

	GetWorkExperiences(ctx context.Context) ([]dtos.ResponseForWorkExperience, error)
	GetWorkExperience(ctx context.Context, id uuid.UUID) (dtos.ResponseForWorkExperience, error)
	CreateWorkExperience(ctx context.Context, req dtos.RequestForCreateWorkExperience) error
	UpdateWorkExperience(ctx context.Context, req dtos.RequestForUpdateWorkExperience) error
	DeleteWorkExperience(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetLanguages(ctx context.Context) ([]dtos.ResponseForLanguage, error) {
	var (
		languages []entities.Language
		resp      []dtos.ResponseForLanguage
	)

	r.db.WithContext(ctx).
		Model(&entities.Language{}).
		Where("cleaner_id = ?", state.GetCurrentID(ctx)).
		Find(&languages)

	if len(languages) == 0 {
		return resp, errors.New(consts.NotFoundLanguage)
	}

	for _, v := range languages {
		resp = append(resp, v.Response())
	}

	return resp, nil
}

func (r *repository) CreateLanguage(ctx context.Context, req dtos.RequestForCreateLanguage) error {
	var control_language entities.Language

	r.db.WithContext(ctx).
		Model(&entities.Language{}).
		Where("cleaner_id = ?", state.GetCurrentID(ctx)).
		Where("code = ?", req.Code).
		First(&control_language)

	if control_language.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistLanguage)
	}

	language := &entities.Language{
		Name:      req.Name,
		Code:      req.Code,
		Level:     req.Level,
		CleanerID: state.GetCurrentID(ctx),
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Language{}).
		Create(language).Error

	return err
}

func (r *repository) DeleteLanguage(ctx context.Context, id uuid.UUID) error {
	var language entities.Language
	r.db.WithContext(ctx).
		Model(&entities.Language{}).
		Where("id = ?", id).
		First(&language)

	if language.ID == uuid.Nil {
		return errors.New(consts.NotFoundLanguage)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.Language{}).
		Delete(&language).Error

	return err
}

func (r *repository) GetWorkExperiences(ctx context.Context) ([]dtos.ResponseForWorkExperience, error) {
	var (
		workExperiences []entities.WorkExperience
		resp            []dtos.ResponseForWorkExperience
	)

	r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("cleaner_id = ?", state.GetCurrentID(ctx)).
		Find(&workExperiences)

	if len(workExperiences) == 0 {
		return resp, errors.New(consts.NotFoundWorkExperience)
	}

	for _, v := range workExperiences {
		resp = append(resp, v.Response())
	}

	return resp, nil
}

func (r *repository) GetWorkExperience(ctx context.Context, id uuid.UUID) (dtos.ResponseForWorkExperience, error) {
	var workExperience entities.WorkExperience

	r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("id = ? AND cleaner_id = ?", id, state.GetCurrentID(ctx)).
		First(&workExperience)

	if workExperience.ID == uuid.Nil {
		return dtos.ResponseForWorkExperience{}, errors.New(consts.NotFoundWorkExperience)
	}

	return workExperience.Response(), nil
}

func (r *repository) CreateWorkExperience(ctx context.Context, req dtos.RequestForCreateWorkExperience) error {

	var control_work_experience entities.WorkExperience

	r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("cleaner_id = ?", state.GetCurrentID(ctx)).
		Where("title = ?", req.Title).
		First(&control_work_experience)

	if control_work_experience.ID != uuid.Nil {
		return errors.New(consts.AlreadyExistWorkExperience)
	}

	workExperience := &entities.WorkExperience{
		Title:       req.Title,
		Company:     req.Company,
		Location:    req.Location,
		Country:     req.Country,
		StartDate:   req.StartDate,
		EndDate:     req.EndDate,
		Description: req.Description,
		Current:     req.Current,
		CleanerID:   state.GetCurrentID(ctx),
	}

	err := r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Create(workExperience).Error

	return err
}

func (r *repository) UpdateWorkExperience(ctx context.Context, req dtos.RequestForUpdateWorkExperience) error {
	var workExperience entities.WorkExperience

	r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("id = ? AND cleaner_id = ?", req.ID, state.GetCurrentID(ctx)).
		First(&workExperience)

	if workExperience.ID == uuid.Nil {
		return errors.New(consts.NotFoundWorkExperience)
	}

	workExperience.Title = req.Title
	workExperience.Company = req.Company
	workExperience.Location = req.Location
	workExperience.Country = req.Country
	workExperience.StartDate = req.StartDate
	workExperience.EndDate = req.EndDate
	workExperience.Description = req.Description
	workExperience.Current = req.Current

	err := r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("id = ?", workExperience.ID).
		Updates(&workExperience).Error

	return err
}

func (r *repository) DeleteWorkExperience(ctx context.Context, id uuid.UUID) error {
	var workExperience entities.WorkExperience
	r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Where("id = ? AND cleaner_id = ?", id, state.GetCurrentID(ctx)).
		First(&workExperience)

	if workExperience.ID == uuid.Nil {
		return errors.New(consts.NotFoundWorkExperience)
	}

	err := r.db.WithContext(ctx).
		Model(&entities.WorkExperience{}).
		Delete(&workExperience).Error

	return err
}
