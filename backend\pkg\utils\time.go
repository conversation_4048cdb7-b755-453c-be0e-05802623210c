package utils

import (
	"time"
)

func CannotBeBeforeCurrentTime(t ...time.Time) bool {
	for _, v := range t {
		if v.Before(time.Now()) {
			return true
		}
	}
	return false
}

func CannotBeBeforeStartTime(start, end time.Time) bool {
	return end.Before(start)
}

func DifferenceBetweenTimes(start, end time.Time) time.Duration {
	return end.Sub(start)
}

func MinXHourAfterCurrentTime(x int, t time.Time) bool {
	return t.Before(time.Now().Add(time.Hour * time.Duration(x)))
}
