package utils

import (
	"testing"
)

func TestTCKimlikNoRegex(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Valid format - 11 digits starting with non-zero",
			input:    "12345678901",
			expected: true,
		},
		{
			name:     "Invalid format - starts with zero",
			input:    "01234567890",
			expected: false,
		},
		{
			name:     "Invalid format - too short",
			input:    "1234567890",
			expected: false,
		},
		{
			name:     "Invalid format - too long",
			input:    "123456789012",
			expected: false,
		},
		{
			name:     "Invalid format - contains letters",
			input:    "1234567890a",
			expected: false,
		},
		{
			name:     "Invalid format - empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "Invalid format - contains spaces",
			input:    "123 456 789 01",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidTCFormat(tt.input)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("IsValidTCFormat(%s) = %v, expected %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestValidateTCKimlikNo(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Valid TC - known valid number",
			input:    "11111111110",
			expected: true,
		},
		{
			name:     "Valid TC - another known valid number",
			input:    "10000000146",
			expected: true,
		},
		{
			name:     "Invalid TC - wrong algorithm",
			input:    "12345678901",
			expected: false,
		},
		{
			name:     "Invalid TC - starts with zero",
			input:    "01234567890",
			expected: false,
		},
		{
			name:     "Invalid TC - too short",
			input:    "1234567890",
			expected: false,
		},
		{
			name:     "Invalid TC - contains letters",
			input:    "1234567890a",
			expected: false,
		},
		{
			name:     "Invalid TC - empty string",
			input:    "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateTCKimlikNo(tt.input)
			if result != tt.expected {
				t.Errorf("ValidateTCKimlikNo(%s) = %v, expected %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestNormalizeTCKimlikNo(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Remove spaces",
			input:    "123 456 789 01",
			expected: "12345678901",
		},
		{
			name:     "Remove dashes",
			input:    "123-456-789-01",
			expected: "12345678901",
		},
		{
			name:     "Remove mixed characters",
			input:    "123 456-789.01",
			expected: "12345678901",
		},
		{
			name:     "Already clean",
			input:    "12345678901",
			expected: "12345678901",
		},
		{
			name:     "Remove letters",
			input:    "123abc456def789",
			expected: "123456789",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizeTCKimlikNo(tt.input)
			if result != tt.expected {
				t.Errorf("NormalizeTCKimlikNo(%s) = %s, expected %s", tt.input, result, tt.expected)
			}
		})
	}
}

func TestValidateTCAlgorithm(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Valid algorithm - 11111111110",
			input:    "11111111110",
			expected: true,
		},
		{
			name:     "Valid algorithm - 10000000146",
			input:    "10000000146",
			expected: true,
		},
		{
			name:     "Invalid algorithm - wrong 10th digit",
			input:    "11111111111",
			expected: false,
		},
		{
			name:     "Invalid algorithm - wrong 11th digit",
			input:    "11111111111",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateTCAlgorithm(tt.input)
			if result != tt.expected {
				t.Errorf("validateTCAlgorithm(%s) = %v, expected %v", tt.input, result, tt.expected)
			}
		})
	}
}
